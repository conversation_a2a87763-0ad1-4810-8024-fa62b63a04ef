###
 # @Description:
 # @Author: wsy
 # @Date: 2023-11-20 09:08:32
 # @LastEditTime: 2023-12-08 19:32:03
 # @LastEditors: wsy
###
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = /data-center-backend
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =

#第三方获取token的api
VITE_AUTH_API_URL = http://*************:6099

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = false
# 是否在打包时开启压缩，支持 gzip 和 brotli，例如 gzip,brotli
VITE_BUILD_COMPRESS = gzip
# 是否启用全局请求加密
VITE_GLOBAL_ENCRYPT_CONFIG = true
