import { basic as request } from "@/api/request";

/**
 * @description 删除PubRoleGroup 根据ID删除PubRoleGroup
 * @url /rbac/pubRoleGroup/delete
 * @method DELETE
 * <AUTHOR>
 */
export function deleteRbacPubRoleGroupDelete<
  R = API.ResponseDataVoid,
  T = API.ResponseDataVoid
>(
  params: API.deleteRbacPubRoleGroupDeleteParams,
  options?: Parameters<typeof request.Delete<R, T>>[2]
) {
  return request.Delete<R, T>(
    "/rbac/pubRoleGroup/delete",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 获取所有PubRoleGroup 返回所有pub_角色组列表
 * @url /rbac/pubRoleGroup/getAll
 * @method GET
 * <AUTHOR>
 */
export function getRbacPubRoleGroupGetAll<
  R = API.ResponseDataListPubRoleGroup,
  T = API.ResponseDataListPubRoleGroup
>(
  params: API.getRbacPubRoleGroupGetAllParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/rbac/pubRoleGroup/getAll", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据ID获取pub_角色组 返回指定ID的pub_角色组
 * @url /rbac/pubRoleGroup/getById
 * @method GET
 * <AUTHOR>
 */
export function getRbacPubRoleGroupGetById<
  R = API.ResponseDataPubRoleGroup,
  T = API.ResponseDataPubRoleGroup
>(
  params: API.getRbacPubRoleGroupGetByIdParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/rbac/pubRoleGroup/getById", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询PubRoleGroup 返回pub_角色组分页
 * @url /rbac/pubRoleGroup/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postRbacPubRoleGroupPageQuery<
  R = API.ResponseDataPageDataPubRoleGroup,
  T = API.ResponseDataPageDataPubRoleGroup
>(
  body: API.PubRoleGroupQueryDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/rbac/pubRoleGroup/pageQuery", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 创建pub_角色组 根据请求体创建一个新的pub_角色组
 * @url /rbac/pubRoleGroup/save
 * @method POST
 * <AUTHOR>
 */
export function postRbacPubRoleGroupSave<
  R = API.ResponseDataPubRoleGroup,
  T = API.ResponseDataPubRoleGroup
>(
  body: API.PubRoleGroupAddDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/rbac/pubRoleGroup/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 更新PubRoleGroup 根据ID更新PubRoleGroup的详细信息
 * @url /rbac/pubRoleGroup/update
 * @method POST
 * <AUTHOR>
 */
export function postRbacPubRoleGroupUpdate<
  R = API.ResponseDataPubRoleGroup,
  T = API.ResponseDataPubRoleGroup
>(
  body: API.PubRoleGroupUpdateDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/rbac/pubRoleGroup/update", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
