import { basic as request } from '@/api/request';

/**
 * @description 租户新增
 * @url /rbac/tenant/pubTenantAdd
 * @method POST
 * <AUTHOR>
 */
export function postRbacTenantPubTenantAdd<
  R = API.ResponseDataString,
  T = API.ResponseDataString,
>(body: API.PubTenantDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/rbac/tenant/pubTenantAdd', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 租户删除
 * @url /rbac/tenant/pubTenantDelete
 * @method GET
 * <AUTHOR>
 */
export function getRbacTenantPubTenantDelete<
  R = API.ResponseDataVoid,
  T = API.ResponseDataVoid,
>(
  params: API.getRbacTenantPubTenantDeleteParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/rbac/tenant/pubTenantDelete', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据id查询租户
 * @url /rbac/tenant/pubTenantInfo
 * @method GET
 * <AUTHOR>
 */
export function getRbacTenantPubTenantInfo<
  R = API.ResponseDataPubTenantBo,
  T = API.ResponseDataPubTenantBo,
>(
  params: API.getRbacTenantPubTenantInfoParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/rbac/tenant/pubTenantInfo', {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 租户分页查询
 * @url /rbac/tenant/pubTenantPage
 * @method POST
 * <AUTHOR>
 */
export function postRbacTenantPubTenantPage<
  R = API.ResponseDataPageDataPubTenant,
  T = API.ResponseDataPageDataPubTenant,
>(
  body: API.PubTenantQueryDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/rbac/tenant/pubTenantPage', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 租户编辑
 * @url /rbac/tenant/pubTenantUpdate
 * @method POST
 * <AUTHOR>
 */
export function postRbacTenantPubTenantUpdate<
  R = API.ResponseDataVoid,
  T = API.ResponseDataVoid,
>(body: API.PubTenantDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/rbac/tenant/pubTenantUpdate', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
