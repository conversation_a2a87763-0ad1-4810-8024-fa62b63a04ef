/* eslint-disable */
// @ts-nocheck
declare namespace API {
  type AddOrEditStyleConfigDto = {
    /** 样式内容 */
    content: string;
  };

  type AddOrUpdateConfigurationItemDTO = {
    /** 配置项内容json */
    content: string;
  };

  type BlackWhiteListAddDto = {
    /** ip地址 */
    ip: string;
    /** 描述 */
    description?: string;
    /** true未启用 false启用 */
    enable: boolean;
    /** 0:黑名单 1白名单 */
    type: string;
  };

  type BlackWhiteListQueryDto = {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** ip地址 */
    ip?: string;
    /** 类型 */
    type?: string;
    /** 启用或停用 */
    enable?: string;
  };

  type BlackWhiteListUpdateDto = {
    /** ip地址 */
    ip: string;
    /** 描述 */
    description?: string;
    /** true未启用 false启用 */
    enable: boolean;
    /** 0:黑名单 1白名单 */
    type: string;
    /** id */
    id: string;
  };

  type deleteDictItemDeleteByIdParams = {
    /** 字典项id */
    id: string;
  };

  type deleteDictTypeDeleteByIdParams = {
    /** 字典类型id */
    id: string;
  };

  type DictItemBatchVo = {
    /** 字典类型 */
    dictCode?: string;
    /** 批量字典项 */
    dictItemList?: DictItemVo[];
  };

  type DictItemQueryDto = {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 字典类型编码 */
    code: string;
    /** 字典项名称 */
    dictLabel?: string;
  };

  type DictItemSaveDto = {
    /** 主键 */
    id?: string;
    /** 字典类型编码 */
    code: string;
    /** 字典项值 */
    dictLabel: string;
    /** 字典项值 */
    dictValue: string;
    /** 描述 */
    description?: string;
    /** 排序 */
    sort?: number;
    /** 是否启用，0：否，1：是 */
    enable?: boolean;
  };

  type DictItemVo = {
    /** 主键 */
    id?: string;
    /** 字典类型编码 */
    code?: string;
    /** 字典项标签名 */
    dictLabel?: string;
    /** 字典项值 */
    dictValue?: string;
    /** 描述 */
    description?: string;
    /** 排序 */
    sort?: number;
    /** 是否启用，0：否，1：是 */
    enable?: boolean;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type DictTypeQueryDto = {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 名称或编码 */
    name?: string;
  };

  type DictTypeSaveDto = {
    /** 主键,新增时不填 */
    id?: string;
    /** 名称 */
    name: string;
    /** 字典类型编码 */
    code: string;
    /** 描述 */
    description?: string;
    /** 排序 */
    sort?: number;
  };

  type DictTypeVo = {
    /** 主键,新增时不填 */
    id?: string;
    /** 名称 */
    name?: string;
    /** 字典类型编码 */
    code?: string;
    /** 描述 */
    description?: string;
    /** 排序 */
    sort?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type EditSysSecurityConfigDto = {
    /** 配置内容 */
    content: string;
  };

  type getBlackWhiteListDeleteByIdParams = {
    /** 黑白名单id */
    id: string;
  };

  type getBlackWhiteListGetBlackWhiteListByIdParams = {
    /** 黑白名单id */
    id: string;
  };

  type getDictItemListBatchParams = {
    /** 字典类型编码,用逗号分隔 */
    dictCode: string;
  };

  type getDictItemListParams = {
    /** 字典类型编码 */
    dictCode: string;
  };

  type getSysconfigDownLoadImageStreamByCodeParams = {
    code: string;
  };

  type IPagePubBlackWhiteList = {
    size?: number;
    current?: number;
    records?: PubBlackWhiteList[];
    total?: number;
    pages?: number;
  };

  type PageDataDictItemVo = {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DictItemVo[];
    total?: number;
  };

  type PageDataDictTypeVo = {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DictTypeVo[];
    total?: number;
  };

  type postDictItemDeleteByIdParams = {
    /** 字典项id */
    id: string;
  };

  type postDictTypeDeleteByIdParams = {
    /** 字典类型id */
    id: string;
  };

  type postSysconfigAddOrUpdateBackgroundImageParams = {
    /** 图片色调，1：亮色，2：暗色 */
    imageType: string;
  };

  type postSysconfigAddOrUpdateBackgroundSmallImageParams = {
    /** 图片色调，1：亮色，2：暗色 */
    imageType: string;
  };

  type PubBlackWhiteList = {
    /** id */
    id?: string;
    /** ip地址 */
    ip?: string;
    /** 描述 */
    description?: string;
    /** 0：黑名单 1：白名单 */
    type?: string;
    /** 排序 */
    sort?: number;
    /** false:未启用 true启用 */
    enable?: boolean;
    /** 删除标识, false:未删除, true删除 */
    deleted?: boolean;
    /** 更新用户id */
    updatedBy?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 创建用户id */
    createdBy?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type PubSysConfig = {
    /** id */
    id?: string;
    /** 配置名称 */
    name?: string;
    /** 配置编码 */
    code?: string;
    /** 配置内容 */
    content?: string;
    /** 0-系统内置，1-用户定义 */
    type?: string;
    /** 排序 */
    sort?: number;
    /** 备注 */
    remark?: string;
    /** 删除标识, false-未删除, true-删除 */
    deleted?: boolean;
    /** 创建用户id */
    createdBy?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新用户id */
    updatedBy?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type ResponseDataBoolean = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: boolean;
    success?: boolean;
  };

  type ResponseDataIPagePubBlackWhiteList = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: IPagePubBlackWhiteList;
    success?: boolean;
  };

  type ResponseDataListDictItemBatchVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DictItemBatchVo[];
    success?: boolean;
  };

  type ResponseDataListDictItemVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DictItemVo[];
    success?: boolean;
  };

  type ResponseDataPageDataDictItemVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDictItemVo;
    success?: boolean;
  };

  type ResponseDataPageDataDictTypeVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDictTypeVo;
    success?: boolean;
  };

  type ResponseDataPubBlackWhiteList = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PubBlackWhiteList;
    success?: boolean;
  };

  type ResponseDataPubSysConfig = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PubSysConfig;
    success?: boolean;
  };

  type ResponseDataString = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: string;
    success?: boolean;
  };

  type ResponseDataVoid = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: Record<string, any>;
    success?: boolean;
  };
}
