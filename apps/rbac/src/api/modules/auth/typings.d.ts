/* eslint-disable */
// @ts-nocheck
declare namespace API {
  type getAuthCheckCacheTokenParams = {
    accessToken: string;
    userId: string;
  };

  type getAuthLoginValidCodeParams = {
    /** 尺寸(长x宽)，例：150x50表示长为150宽为50，不填默认150x50 */
    size?: string;
    /** 验证码长度，不填默认4位 */
    length?: number;
  };

  type IResponseCode = {
    code?: number;
    msg?: string;
    hint?: string;
  };

  type LoginByPwdDto = {
    /** 登录类型 */
    loginType: string;
    /** 登录名 */
    loginName: string;
    /** 密码 */
    loginPwd: string;
    /** 验证码hash */
    codeHash: string;
    /** 验证码 */
    code: string;
  };

  type LoginTokenVo = {
    /** 刷新令牌 */
    refreshToken?: string;
    /** 访问令牌 */
    accessToken?: string;
    loginResponse?: IResponseCode;
  };

  type postAuthChangeLoginOrgParams = {
    /** 新组织id */
    orgId: string;
  };

  type postAuthTokenRefreshParams = {
    /** 刷新令牌 */
    refreshToken: string;
  };

  type ResponseDataBoolean = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: boolean;
    success?: boolean;
  };

  type ResponseDataLoginTokenVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: LoginTokenVo;
    success?: boolean;
  };

  type ResponseDataString = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: string;
    success?: boolean;
  };

  type ResponseDataValidCodeVo = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: ValidCodeVo;
    success?: boolean;
  };

  type ResponseDataVoid = {
    code?: number;
    msg?: string;
    hint?: string;
    data?: Record<string, any>;
    success?: boolean;
  };

  type ValidCodeVo = {
    key?: string;
    plaintext?: string;
  };
}
