/* eslint-disable */
// @ts-nocheck
declare namespace API {
  interface AdhocPageDataMapStringObject {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: Record<string, any>[];
    total?: number;
    /** 查询结果列名列表 */
    headers?: string[];
  }

  interface AdhocQueryPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 可使用uuid,同样查询条件，只改变分页时不变，改变表或者查询条件时重新生成一个 */
    key?: string;
    /** 查询sql语句 */
    sql?: string;
    /** 数据源及表信息列表 */
    dbSourceAndTablesList?: DbSourceAndTables[];
  }

  interface AdhocQueryResultPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 和创建即席查询任务参数key一致 */
    key?: string;
  }

  interface ApiInfoVo {
    /** 接口版本号列表 */
    versions?: ApiVersionVO[];
    /** 接口输入参数列表 */
    inDocList?: ApiNodeInDocVO[];
    /** 接口返回字段列表 */
    outDocVOList?: ApiNodeOutDocVO[];
  }

  interface ApiLog {
    id?: number;
    /** 应用商应用ID */
    userId?: number;
    /** 应用商应用名称 */
    userName?: string;
    /** 数据接口id */
    apiId?: number;
    /** 数据接口名称 */
    apiName?: string;
    /** 当天时间，yyyyMMdd */
    visitDate?: string;
    /** 当天时间,  HH 24小时制 */
    visitHour?: string;
    /** 耗时，单位毫秒 */
    usedTime?: number;
    /** 访问IP地址 */
    remoteIp?: string;
    /** 访问时间yyyy-MM-dd HH:hh:ss */
    visitTime?: string;
    /** 处理状态 0:正常 | 1:异常 */
    result?: number;
    /** 错误信息 */
    errorInfo?: string;
    /** 请求参数 */
    requestParams?: string;
    /** 返回参数大小(byte) */
    dataSize?: number;
  }

  interface ApiLogQueryDto {
    /** 应用id */
    userId?: number;
    /** 应用名称 */
    userName?: string;
    /** 接口id */
    apiId?: number;
    /** 接口名称 */
    apiName?: string;
    /** 查询开始时间, yyyy-MM-dd HH:mm */
    startTime?: string;
    /** 查询结束时间, yyyy-MM-dd HH:mm */
    endTime?: string;
    /** 当前页 */
    pageNum: number;
    /** 每页行数 */
    pageSize: number;
  }

  interface ApiNodeDO {
    /** 节点名称 */
    name?: string;
    /** 模型id */
    modelId?: number;
    /** 节点接口地址 */
    url?: string;
    /** 返回数据是否加密0:不加密 1:加密 */
    returnIsEncr?: number;
    /** 是否排序0:不排序1:排序 */
    isOrder?: number;
    /** 排序类型 desc：降序  asc：升序 */
    orderType?: string;
    /** 是否缓存0:缓存1:不缓存 */
    isCache?: number;
    /** 返回类型 */
    returnType?: string;
    /** 加密秘钥 */
    secretKey?: string;
    /** 排序字段 */
    orderClum?: string;
    /** 排序 */
    sort?: number;
    /** 返回结果是否分页 */
    isPage?: number;
    /** 是否去重，0:不去重 1:去重 */
    isDistinct?: number;
    /** 节点接口类型 0:读 1:写 默认为读 */
    type?: number;
    /** 接口请求方法 POST GET */
    requestMethod?: string;
    /** body 格式json,form,x-www-form-urlencoded */
    requestBodyType?: string;
    /** 被代理的接口uri 格式 https://ip:port */
    uri?: string;
    /** 被代理的接口路径 */
    uriToPath?: string;
    /** 数据来源分类，0：根据主题域模型创建接口（默认），1：代理接口类型 */
    dataSourceType?: number;
    /** 元数据id */
    tableId?: number;
    /** API接口节点入参参数List */
    apiNodeInList?: ApiNodeInSaveDO[];
    /** API接口节点出参参数List */
    apiNodeOutList?: ApiNodeOutSaveDO[];
    apiNodePublishSave?: ApiNodePublishSaveDO;
  }

  interface ApiNodeDocumentVO {
    /** 节点名称 */
    name?: string;
    /** 节点接口地址 */
    url?: string;
    /** 版本号 */
    version?: string;
    /** 请求方式 */
    requestWay?: string;
    /** 请求入参bean */
    inDocList?: ApiNodeInDocVO[];
    /** 请求入参json示例 */
    inExamples?: string;
    /** 请求出参bean */
    outDocList?: ApiNodeOutDocVO[];
    /** 请求出参json示例 */
    outExamples?: string;
    /** 摘要 */
    summary?: string;
    /** 请求头信息 */
    headerInVOList?: HeaderInVO[];
    /** 更新时间 */
    updateTime?: string;
  }

  interface ApiNodeInDocVO {
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数值 */
    paraValue?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 是否必选0：不必选 1：必选 */
    isMandatory?: number;
    /** 参数别名 */
    paraAlias?: string;
    /** 是否隐藏0：不隐藏 1：隐藏 */
    isHide?: number;
    /** 是否可以多参数传值0：不可，1：可以 */
    isIn?: number;
    /** 多参数传值分隔符 */
    splitChar?: string;
  }

  interface ApiNodeInSaveDO {
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数值 */
    paraValue?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 参数来源0：外部 1：API返回 */
    paraFrom?: number;
    /** 是否隐藏0：不隐藏 1：隐藏 */
    isHide?: number;
    /** 是否必选0：不必选 1：必选 */
    isMandatory?: number;
    /** 参数别名 */
    paraAlias?: string;
    /** 参数规则 >,=,<,in */
    paraRule?: string;
    /** in: 值;值;值 */
    inValue?: string;
    /** 是否可以多参数传值0：不可，1：可以 */
    isIn?: number;
    /** 多参数传值分隔符 */
    splitChar?: string;
  }

  interface ApiNodeInVO {
    /** api接口节点入参id */
    id?: number;
    /** 节点id */
    nodeId?: number;
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数值 */
    paraValue?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 参数来源0：外部 1：API返回 */
    paraFrom?: number;
    /** 是否隐藏0：不隐藏 1：隐藏 */
    isHide?: number;
    /** 是否必选0：不必选 1：必选 */
    isMandatory?: number;
    /** 参数别名 */
    paraAlias?: string;
    /** 参数规则 >,=,<,in */
    paraRule?: string;
    /** in: 值;值;值 */
    inValue?: string;
    /** 是否可以多参数传值0：不可，1：可以 */
    isIn?: number;
    /** 多参数传值分隔符 */
    splitChar?: string;
  }

  interface ApiNodeOutDocVO {
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 是否为空0:为空1:不为空 */
    isEmpty?: number;
  }

  interface ApiNodeOutSaveDO {
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 是否为空0:为空1:不为空 */
    isEmpty?: number;
    /** 显示排序 0:排序1:不排序 */
    isSort?: number;
  }

  interface ApiNodeOutVO {
    /** API接口节点出参id */
    id?: number;
    /** 节点id */
    nodeId?: number;
    /** 参数名称 */
    paraName?: string;
    /** 参数类型 */
    paraType?: string;
    /** 参数描述 */
    paraDesc?: string;
    /** 是否为空0:为空1:不为空 */
    isEmpty?: number;
    /** 显示排序 0:排序1:不排序 */
    isSort?: number;
  }

  interface ApiNodePublishSaveDO {
    /** 数据板块id */
    dataProjectId?: number;
    /** 主题域id */
    dataProjectDomainId?: number;
    /** 生命周期 */
    liveTime?: string;
  }

  interface ApiNodeVO {
    /** api接口节点id */
    id?: number;
    /** 节点名称 */
    name?: string;
    /** 模型id */
    modelId?: number;
    /** 模型名称 */
    modelName?: string;
    /** 节点接口地址 */
    url?: string;
    /** 返回数据是否加密0:不加密 1:加密 */
    returnIsEncr?: number;
    /** 是否排序0:不排序1:排序 */
    isOrder?: number;
    /** 排序类型 desc：降序  asc：升序 */
    orderType?: string;
    /** 是否缓存0:缓存1:不缓存 */
    isCache?: number;
    /** 返回类型 */
    returnType?: string;
    /** 加密秘钥 */
    secretKey?: string;
    /** 排序字段 */
    orderClum?: string;
    /** 排序 */
    sort?: number;
    /** 返回结果是否分页 */
    isPage?: number;
    /** 是否去重，0:不去重 1:去重 */
    isDistinct?: number;
    /** 节点接口类型 0:读 1:写 默认为读 */
    type?: number;
    /** 接口请求方法 POST GET */
    requestMethod?: string;
    /** body 格式json,form,x-www-form-urlencoded */
    requestBodyType?: string;
    /** 被代理的接口uri 格式 https://ip:port */
    uri?: string;
    /** 被代理的接口路径 */
    uriToPath?: string;
    /** API接口节点入参参数List */
    apiNodeInList?: ApiNodeInVO[];
    /** API接口节点出参参数List */
    apiNodeOutList?: ApiNodeOutVO[];
    apiNodeDocVO?: ApiNodeDocumentVO;
    /** 数据来源id */
    tableId?: number;
    /** 数据来源分类，0：根据主题域模型创建接口（默认），1：代理接口类型 */
    dataSourceType?: number;
    apiNodePublishSave?: ApiNodePublishSaveDO;
  }

  interface ApiSetAddParams {
    /** 任务名称 */
    name: string;
    /** client ID */
    clientId: string;
    /** client Secret */
    clientSecret: string;
    /** 备注 */
    remark?: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据类型：1表数据 2消息队列 */
    tableType: number;
  }

  interface ApiSetUpdateParams {
    /** id */
    id: number;
    /** 任务名称 */
    name: string;
    /** client ID */
    clientId: string;
    /** client Secret */
    clientSecret: string;
    /** 备注 */
    remark?: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据类型：1表数据 2消息队列 */
    tableType: number;
  }

  interface ApiVersionVO {
    /** 对应的api版本 */
    version?: string;
    /** Api接口id */
    id?: number;
  }

  interface AppApiLogDO {
    /** 当前页 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
    /** 应用id */
    appId?: number;
    /** 应用名称 */
    appName?: string;
    /** 接口id */
    apiId?: number;
    /** 接口名称 */
    apiName?: string;
    /** 选择日期,yyyyMMdd */
    visitDate?: string;
    /** 起始日期, yyyyMMdd */
    startTime?: string;
    /** 结束日期，yyyyMMdd */
    endTime?: string;
    /** 响应类别,1：非正常，0：正常 */
    resultType?: number;
    /** 最小传输时间 */
    minTime?: number;
  }

  interface AppApiSqlTestParam {
    /** 接口id */
    parentApiId?: number;
    /** 接口版本号 */
    version?: string;
    /** sql */
    sql?: string;
    /** sql参数，key->value格式 */
    params?: Record<string, any>;
  }

  interface AppLog {
    id?: number;
    应用名?: string;
    ip?: string;
    端口?: string;
    类型?: string;
    日志信息?: string;
    错误堆栈?: string;
    logtime?: string;
    日志记录点?: string;
    线程名?: string;
    createTime?: string;
  }

  interface AppLogFileVo {
    /** 应用名称 */
    name?: string;
    /** 日志类型：info，warn，error */
    type?: string;
    /** 查询开始时间，yyyy-MM-dd HH:mm */
    startTime?: string;
    /** 查询结束时间，yyyy-MM-dd HH:mm */
    endTime?: string;
    /** 页码 */
    page?: number;
    /** 单页表单数据行数 */
    rows?: number;
  }

  interface AuthDto {
    userId: number;
    apiId: number[];
  }

  interface AuthUserDto {
    apiId?: number;
    userId?: number[];
  }

  interface BasePubUserDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 登录账号 */
    loginName?: string;
    /** 真实名称 */
    realName?: string;
    /** 主组织 */
    orgId?: string;
  }

  interface CollectTableSourceVo {
    /** 对应数据源id */
    dbSourceId?: number;
    /** 数据库真实表名 */
    tableName?: string;
    /** 数据源名称 */
    name?: string;
    /** 数据源类型 */
    sourceType?: string;
  }

  interface DataApiDO {
    /** 当前页 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
    /** 接口类型 */
    tableId?: number;
    /** 接口名称 */
    name?: string;
  }

  interface DataApiListDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 所属数据板块id */
    projectId?: number;
    /** 表所属主题域id */
    domainId?: number;
    name?: string;
  }

  interface DataApiListVo {
    id?: number;
    tableId?: number;
    code?: string;
    summary?: string;
    keyWord?: string;
    frequency?: number;
    fromSystem?: string;
    publishTime?: string;
    updateTime?: string;
    name?: string;
    isValid?: number;
    isDel?: number;
    version?: string;
    isView?: number;
    parentApiId?: number;
    updateDetails?: string;
    /** 数据板块id */
    dataProjectId?: number;
    /** 主题域id */
    dataProjectDomainId?: number;
    /** 生命周期 */
    liveTime?: string;
    /** 数据来源分类，0：根据主题域模型创建接口（默认），1：代理接口类型 */
    dataSourceType?: number;
    /** 访问 */
    visit?: number;
    /** 编辑 */
    edit?: number;
    /** 开放 */
    open?: number;
    /** 审计 */
    audit?: number;
  }

  interface DataApiSaveDO {
    /** 编码 */
    code?: string;
    /** 摘要 */
    summary?: string;
    /** 关键字 */
    keyWord?: string;
    /** 更新频率 */
    frequency?: string;
    /** 来源系统 */
    fromSystem?: string;
    /** 接口名称 */
    name?: string;
    /** 是否有效 0:有效 1:无效 */
    isValid?: number;
    /** 是否展示 0:展示 1:不展示 */
    isView?: number;
    /** API接口节点数据list */
    apiNodeList?: ApiNodeDO[];
    /** 接口id */
    apiId?: number;
  }

  interface DataApiUserDo {
    userName?: string;
    name?: string;
    apiId?: number;
    authorization: number;
  }

  interface DataApiUserDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    userId?: number;
    apiName?: string;
    apiId?: number;
    projectid?: number;
    domainId?: number;
  }

  interface DataApiVO {
    /** api接口id */
    id?: number;
    /** 数据表id */
    tableId?: number;
    /** 数据表版本号 */
    tableVersion?: string;
    /** 数据表数据板块id */
    projectId?: string;
    projectName?: string;
    /** 数据表主题域id */
    domainId?: string;
    domainName?: string;
    /** 接口分类名称 */
    typeName?: string;
    /** 编码 */
    code?: string;
    /** 摘要 */
    summary?: string;
    /** 关键字 */
    keyWord?: string;
    /** 更新频率 */
    frequency?: number;
    /** 来源系统 */
    fromSystem?: string;
    /** 接口名称 */
    name?: string;
    /** 是否有效 0:有效 1:无效 */
    isValid?: number;
    /** 是否展示 0:展示 1:不展示 */
    isView?: number;
    /** 是否更新版本 0:更新 1:不更新 */
    isUpdateVersion?: number;
    /** API接口节点数据list */
    apiNodeList?: ApiNodeVO[];
    /** 接口id */
    apiId?: number;
    tableName?: string;
    dataProjectId?: string;
    dataProjectName?: string;
    dataProjectDomainId?: string;
    dataProjectDomainName?: string;
  }

  interface DataAssetPermissionDetailDto {
    /** 用户id */
    owner: string;
    /** 资产类型 */
    type: string;
    /** 数据主题域id */
    id: number;
  }

  interface DataAssetPermissionDto {
    /** 用户id */
    owner: string;
    /** 权限列表 */
    list: DataAssetPermissionListDto[];
  }

  interface DataAssetPermissionHistory {
    /** id */
    id?: number;
    /** 结果集 */
    result?: string;
    /** 绑定用户id */
    owner?: string;
    /** 创建人 */
    createBy?: string;
    /** 创建时间 */
    createdat?: string;
  }

  interface DataAssetPermissionListDto {
    /** id */
    id?: number;
    /** 类型 */
    type: string;
    /** 资产id */
    assetId: number;
    /** 访问（默认0否） */
    visit?: number;
    /** 编辑（默认0否） */
    edit?: number;
    /** 开放（默认0否） */
    open?: number;
    /** 审计（默认0否） */
    audit?: number;
  }

  interface DataAssetPermissionListVo {
    /** id */
    id?: number;
    /** 类型 */
    type?: string;
    /** 资产id */
    assetId?: number;
    /** 访问 */
    visit?: number;
    /** 编辑 */
    edit?: number;
    /** 开放 */
    open?: number;
    /** 审计 */
    audit?: number;
    /** 名称 */
    name?: string;
  }

  interface DatabaseSaveDto {
    /** 新增时 id 为空 */
    id?: number;
    name?: string;
    /** 类型(MySQL, Oracle等) */
    type?: string;
    /** 多个url使用逗号分隔 */
    url?: string;
    username?: string;
    password?: string;
    port?: string;
    dbName?: string;
  }

  interface DataElementDto {
    /** 唯一标识符 */
    id?: number;
    /** 中文名称 */
    chineseName: string;
    /** 英文名称 */
    englishName: string;
    /** 定义 */
    definition?: string;
    /** 数据类型 */
    dataType: string;
    /** 长度 */
    length: number;
    /** 值域，0-不定义 1-关联字典表 2-自定义 */
    valueDomain: number;
    /** 字典ID，用于关联字典表 */
    dictId?: number;
    /** 取值范围 */
    valueRange?: string;
    /** 字段映射表 */
    fieldMappingList: FieldMapping[];
    /** 安全密级ID，用于关联安全密级表 */
    securityLevelId: number;
    /** 敏感标签ID */
    sensitiveLabelId?: number;
    /** 脱敏模板类型ID（简写） */
    desensitizationTid?: number;
    /** 版本 */
    version?: string;
    /** 生命周期 */
    lifecycle: number;
    /** 分类目录ID，用于关联分类目录表 */
    directoryId: number;
    /** 分类目录节点类型 1-(主题域) 2-(目录节点) */
    directoryNodeType: number;
    /** 负责人ID */
    personInChargeId: string;
  }

  interface DataElementHistoryVo {
    /** 数据元id */
    elementId?: number;
    /** 历史版本id */
    historyVersionId?: number;
    /** 版本 */
    version?: string;
    /** 提交人 */
    presenter?: string;
    /** 提交时间 */
    submissionTime?: string;
  }

  interface DataElementListVo {
    /** 唯一标识符 */
    id?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 英文名称 */
    englishName?: string;
    /** 数据类型 */
    dataType?: string;
    /** 长度 */
    length?: number;
    /** 版本 */
    version?: string;
    /** 状态 */
    status?: number;
    /** 状态 草稿 已下线 已发布 */
    statusStr?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface DataElementQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 目录id */
    directoryId?: number;
    /** 目录节点类型 */
    directoryNodeType?: number;
    /** 名称 */
    name?: string;
    /** 状态 */
    status?: number;
    /** 数据元id */
    elementId: number;
    /** 历史版本id */
    historyVersionId?: number;
  }

  interface DataIntegrationMethodRealTimeAddDto {
    /** 任务名称 */
    name: string;
    /** 来源数据板块ID */
    sourceProjectId: number;
    /** 来源主题域id */
    sourceDomainId: string;
    /** 来源表id */
    sourceDomainTableId: number;
    /** 来源表-表名 */
    sourceTableName: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 虚拟表名 */
    virtualTableName: string;
    /** SQL数据集语句 */
    sqlData: string;
    /** SQL插入语句 */
    sqlInsert: string;
    /** 任务节点 */
    taskNode?: string;
    /** 消费组 */
    groupId: string;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodRealTimeEditDto {
    /** id */
    id: number;
    /** 任务名称 */
    name: string;
    /** 来源数据板块ID */
    sourceProjectId: number;
    /** 来源主题域id */
    sourceDomainId: string;
    /** 来源表id */
    sourceDomainTableId: number;
    /** 来源表-表名 */
    sourceTableName: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 虚拟表名 */
    virtualTableName: string;
    /** SQL数据集语句 */
    sqlData: string;
    /** SQL插入语句 */
    sqlInsert: string;
    /** 任务节点 */
    taskNode?: string;
    /** 消费组 */
    groupId: string;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodRealTimePreviewDto {
    /** 来源数据列表 */
    sourceList?: DataIntegrationMethodSqlTablesDto[];
    /** SQL数据集语句 */
    sqlData: string;
    /** 来源表id */
    sourceDomainTableId?: number;
    /** 来源表-表名 */
    sourceTableName?: string;
    /** 消费组 */
    groupId?: string;
  }

  interface DataIntegrationMethodSqlAddDto {
    /** 任务名称 */
    name: string;
    /** 虚拟表名 */
    virtualTableName: string;
    /** 来源数据列表 */
    sourceList?: DataIntegrationMethodSqlTablesDto[];
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据处理批次大小 */
    batchSize: number;
    /** SQL数据集语句 */
    sqlData: string;
    /** SQL插入语句 */
    sqlInsert: string;
    /** 增量同步 0是，1否 */
    increased: number;
    /** 增量同步字段 */
    increaseColumn?: string;
    /** 增量同步初始值 */
    increaseValue?: string;
    /** 同步类型: 0:update 1:del&insert */
    processType: string;
    /** 开始时间 */
    startTime: string;
    /** 间隔时长 */
    delay?: number;
    /** 间隔时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 执行方式(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每天（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 循环次数 */
    runCount?: number;
    /** 任务节点 */
    taskNode?: string;
    /** 并行度 */
    parallelism?: number;
    /** 批式/流式 0批式 1流式 */
    batchFlow?: number;
    /** 增量类型 */
    increaseType?: number;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodSqlEditDto {
    /** id */
    id: number;
    /** 任务名称 */
    name: string;
    /** 虚拟表名 */
    virtualTableName: string;
    /** 来源数据列表 */
    sourceList?: DataIntegrationMethodSqlTablesDto[];
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据处理批次大小 */
    batchSize: number;
    /** SQL数据集语句 */
    sqlData: string;
    /** SQL插入语句 */
    sqlInsert: string;
    /** 增量同步 0是，1否 */
    increased: number;
    /** 增量同步字段 */
    increaseColumn?: string;
    /** 增量同步初始值 */
    increaseValue?: string;
    /** 同步类型: 0:update 1:del&insert */
    processType: string;
    /** 开始时间 */
    startTime: string;
    /** 间隔时长 */
    delay?: number;
    /** 间隔时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 执行方式(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每天（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 循环次数 */
    runCount?: number;
    /** 任务节点 */
    taskNode?: string;
    /** 并行度 */
    parallelism?: number;
    /** 批式/流式 0批式 1流式 */
    batchFlow?: number;
    /** 增量类型 */
    increaseType?: number;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodSqlPageDto {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /**  最新执行结果 SUCCESS:成功,FAILURE:失败,RUNNING:运行中 */
    latestResults?: string;
    /** 任务名称 */
    name?: string;
    /** 创建时间 格式yyyy-MM-dd,yyyy-MM-dd */
    createTime?: string;
  }

  interface DataIntegrationMethodSqlPreviewDto {
    /** 任务名称 */
    name?: string;
    /** 来源数据列表 */
    sourceList?: DataIntegrationMethodSqlTablesDto[];
    /** SQL数据集语句 */
    sqlData: string;
    /** 虚拟表名 */
    virtualTableName?: string;
  }

  interface DataIntegrationMethodSqlTablesDto {
    /** 来源数据板块ID */
    sourceProjectId?: number;
    /** 来源主题域id */
    sourceDomainId?: string;
    /** 来源表id */
    sourceDomainTableId?: number;
    /** 来源表-表名 */
    sourceTableName?: string;
  }

  interface DataIntegrationMethodTableAddDto {
    /** 任务名称 */
    name: string;
    /** 任务描述 */
    remark?: string;
    /** 源端数据板块ID */
    sourceProjectId: number;
    /** 源端主题域id */
    sourceDomainId: string;
    /** 源端表id */
    sourceDomainTableId: number;
    /** 源端表-表名 */
    sourceTableName: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据处理批次大小 */
    batchSize: number;
    /** 第三步-字段映射列表 */
    step3: DataIntegrationMethodTableStep3[];
    /** 增量同步 0是，1否 */
    increased: number;
    /** 增量同步字段 */
    increaseColumn?: string;
    /** 增量同步初始值 */
    increaseValue?: string;
    /** 同步类型: 0:update 1:del&insert */
    processType: string;
    /** 开始时间 */
    startTime: string;
    /** 间隔时长 */
    delay?: number;
    /** 间隔时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 执行方式(1:单次任务，2:,3:指定时间循环) */
    type: number;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每天（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 循环次数 */
    runCount?: number;
    /** 任务节点 */
    taskNode?: string;
    /** 并行度 */
    parallelism?: number;
    /** 批式/流式 0批式 1流式 */
    batchFlow?: number;
    /** 增量类型 */
    increaseType?: number;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodTableEditDto {
    /** id */
    id: number;
    /** 任务名称 */
    name: string;
    /** 任务描述 */
    remark?: string;
    /** 源端数据板块ID */
    sourceProjectId: number;
    /** 源端主题域id */
    sourceDomainId: string;
    /** 源端表id */
    sourceDomainTableId: number;
    /** 源端表-表名 */
    sourceTableName: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据处理批次大小 */
    batchSize: number;
    /** 第三步-字段映射列表 */
    step3: DataIntegrationMethodTableStep3[];
    /** 增量同步 0是，1否 */
    increased: number;
    /** 增量同步字段 */
    increaseColumn?: string;
    /** 增量同步初始值 */
    increaseValue?: string;
    /** 同步类型: 0:update 1:del&insert */
    processType: string;
    /** 开始时间 */
    startTime: string;
    /** 间隔时长 */
    delay?: number;
    /** 间隔时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type?: number;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每天（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 循环次数 */
    runCount?: number;
    /** 任务节点 */
    taskNode?: string;
    /** 并行度 */
    parallelism?: number;
    /** 批式/流式 0批式 1流式 */
    batchFlow?: number;
    /** 增量类型 */
    increaseType?: number;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataIntegrationMethodTableMappingDto {
    /** 来源表id */
    sourceDomainTableId: number;
    /** 目标表id */
    targetDomainTableId: number;
  }

  interface DataIntegrationMethodTableStep3 {
    /** 源表字段名 */
    sourceName?: string;
    /** 源表字段类型 */
    sourceType?: string;
    /** 源表字段注释 */
    sourceComments?: string;
    /** 目标表字段名 */
    targetName?: string;
    /** 目标表字段类型 */
    targetType?: string;
    /** 目标表字段注释 */
    targetComments?: string;
    /** 字段顺序 */
    columnsOrder?: number;
  }

  interface DataMetaModelTaskDto {
    /** 任务节点 */
    taskNode?: string;
    /** 失败重试次数 */
    failRetryInterval?: number;
    /** 失败重试间隔 */
    failRetryTimes?: number;
    /** CPU配额 */
    cpuQuota?: number;
    /** 最大内存 */
    memoryMax?: number;
    /** 数仓模型id */
    warehouseModelId: number;
  }

  interface DataMetaTableCollectPage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 运行批次号 */
    batchNo?: string;
  }

  interface DataMetaTableCollectVo {
    /** 主键id */
    id?: number;
    /** 分区号(数据源ID) */
    dbSourceId?: number;
    /** 采集任务实例id */
    taskInstId?: number;
    /** 元数据版本号 */
    version?: number;
    /** 表类型: table | view */
    tableType?: string;
    /** 表名 */
    tableName?: string;
    tableColumns?: JsonNode;
    tableConstraints?: JsonNode;
    /** 建表语句 */
    tableSql?: string;
    tableLineage?: JsonNode;
    /** 该版本是否已注册: 1:已注册,0:未注册 */
    publish?: number;
    changeLog?: JsonNode;
    /** 是否为最新: 1:最新,0:过期 */
    status?: number;
    /** 创建时间 */
    createdAt?: string;
    /** 是否变更 */
    isChange?: boolean;
  }

  interface DataMetaTableDto {
    /** 元数据ID,添加时无需填写，编辑时需要传入 */
    id?: number;
    /** 数据源ID */
    dbSourceId: number;
    /** 采集元数据信息id */
    tableCollectId: number;
    /** 中文名称 */
    chineseName: string;
    /** 英文名称 */
    englishName: string;
    /** 对象描述 */
    description: string;
    /** 主题域 */
    domainId: number;
    /** 数据板块 */
    projectId: number;
    /** 安全密级 */
    securityLevel?: string;
    tableColumns?: JsonNode;
    /** 元数据版本号 */
    version?: string;
    /** 状态：0-草稿，1-发布 */
    status: number;
    /** 资产管理者 */
    adminOwner: string;
    /** 生命周期时长(单位为天，0表示永久有效) */
    cycleDuration?: number;
    /** 是否覆盖数据（默认为false） */
    cover?: boolean;
    /** 数据源类型 */
    sourceType: string;
  }

  interface DataMetaTableLogDto {
    /** ds项目编码 */
    dsProjectCode: number;
    /** ds中工作流实例id */
    dsProcessInstanceId: number;
    /** 限制行数，后端默认100 */
    limitLineNum?: number;
    /** 跳过行数，后端默认0 */
    skipLineNum?: number;
  }

  interface DataMetaTablePage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 元数据名称 */
    content?: string;
    /** 状态：0-草稿，1-发布，2-下线 */
    status?: number;
    /** 数据板块 */
    projectId?: number;
  }

  interface DataMetaTablePageVo {
    /** 主键，ID */
    id?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 表名 */
    tableName?: string;
    /** 状态：0-草稿，1-发布，2-下线 */
    status?: number;
    /** 资产管理者 */
    adminOwnerName?: string;
    /** 元数据版本号 */
    version?: string;
    /** 更新时间 */
    updatedAt?: string;
  }

  interface DataMetaTablePublish {
    /** 删除状态，0：正常；1：删除 */
    deleted?: number;
    /** 创建者ID */
    createBy?: string;
    /** 创建时间 */
    createdAt?: string;
    /** 更新者ID */
    updateBy?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 主键ID */
    id?: number;
    /** 分区号(数据源ID) */
    dbSourceId?: number;
    /** 采集元数据信息id */
    tableCollectId?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 英文名称 */
    englishName?: string;
    /** 对象描述 */
    description?: string;
    /** 主题域 */
    domainId?: number;
    /** 数据板块 */
    projectId?: number;
    /** 安全密级 */
    securityLevel?: string;
    /** 表类型: table | view */
    tableType?: string;
    /** 表名 */
    tableName?: string;
    tableColumns?: JsonNode;
    tableConstraints?: JsonNode;
    /** 建表语句 */
    tableSql?: string;
    tableLineage?: JsonNode;
    /** 元数据版本号 */
    version?: string;
    /** 状态：0-草稿，1-发布，2-下线 */
    status?: number;
    /** 截止时间 */
    deadline?: string;
    /** 发布时间 */
    publishedAt?: string;
    /** 资产管理者 */
    adminOwner?: string;
    /** 初始任务ID */
    initId?: number;
    /** 生命生命周期时长(单位为天，0表示永久有效) */
    cycleDuration?: number;
    /** 数仓模型id */
    warehouseModelId?: number;
    draft?: boolean;
  }

  interface DataMetaTableVo {
    /** 注册的元数据ID */
    id?: number;
    /** 分区号(数据源ID) */
    dbSourceId?: number;
    /** 采集元数据信息id */
    tableCollectId?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 英文名称 */
    englishName?: string;
    /** 对象描述 */
    description?: string;
    /** 主题域 */
    domainId?: number;
    /** 数据板块 */
    projectId?: number;
    /** 安全密级 */
    securityLevel?: string;
    /** 表类型: table | view */
    tableType?: string;
    /** 表名 */
    tableName?: string;
    tableColumns?: JsonNode;
    tableConstraints?: JsonNode;
    /** 建表语句 */
    tableSql?: string;
    tableLineage?: JsonNode;
    /** 元数据版本号 */
    version?: string;
    /** 状态：0-草稿，1-发布，2-下线 */
    status?: number;
    /** 截止时间 */
    deadline?: string;
    /** 发布时间 */
    publishedAt?: string;
    /** 资产管理者 */
    adminOwner?: string;
    /** 资产管理者名称 */
    adminOwnerName?: string;
    /** 初始任务ID */
    initId?: number;
    /** 生命生命周期时长(单位为天，0表示永久有效) */
    cycleDuration?: number;
    /** 创建时间 */
    createdAt?: string;
    /** 创建者ID */
    createBy?: string;
    /** 数据源类型 */
    sourceType?: string;
    /** 数据源名称 */
    sourceName?: string;
    /** 字段信息Json格式化字符串 */
    tableColumn?: string;
  }

  interface DataMetaTaskDto {
    /** 采集任务ID,添加时无需填写，编辑时需要传入 */
    id?: number;
    /** 采集任务名称 */
    taskName: string;
    /** 采集任务备注 */
    remark?: string;
    /** 数据源分类 */
    dbSourceType: string;
    /** 数据源ID */
    dbSourceId: number;
    /** 表名 */
    tableNames: string[];
    taskPlanDto?: DataMetaTaskPlanDto;
    /** 任务节点 */
    taskNode?: string;
    /** 失败重试次数 */
    failRetryInterval?: number;
    /** 失败重试间隔 */
    failRetryTimes?: number;
    /** CPU配额 */
    cpuQuota?: number;
    /** 最大内存 */
    memoryMax?: number;
    /** 数仓模型id */
    warehouseModelId?: number;
    /** 采集类型1模型采集 */
    type?: number;
  }

  interface DataMetaTaskPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数据源分类 */
    dbSourceType?: string;
    /** 数据源ID */
    dbSourceId?: number;
    /** 采集任务名称 */
    content?: string;
  }

  interface DataMetaTaskPageVo {
    /** 任务ID */
    id?: number;
    /** 采集任务名称 */
    taskName?: string;
    /** 数据源分类 */
    dbSourceType?: string;
    /** 数据源名称 */
    name?: string;
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type?: number;
    /** 状态：1-上线，0-下线 */
    status?: number;
    /** 创建者 */
    realName?: string;
    /** 运行状态：1:未运行，2:正在启动，3:运行中，4:正在停止 */
    runStatus?: number;
    /** 工作流id */
    workflowDefineId?: number;
    /** 任务总线id */
    missionBusId?: number;
  }

  interface DataMetaTaskPlanDto {
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 任务间隔 */
    delay?: number;
    /** 延迟时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每日（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 执行次数 */
    runCount?: number;
  }

  interface DataMetaTaskVo {
    /** 采集任务ID */
    id?: number;
    /** 采集任务名称 */
    taskName?: string;
    /** 采集任务备注 */
    remark?: string;
    /** 数据源分类 */
    dbSourceType?: string;
    /** 数据源ID */
    dbSourceId?: number;
    /** 表名 */
    tableNames?: string[];
    taskPlanDto?: DataMetaTaskPlanDto;
    /** 任务节点 */
    taskNode?: string;
    /** 失败重试次数 */
    failRetryInterval?: number;
    /** 失败重试间隔 */
    failRetryTimes?: number;
    /** CPU配额 */
    cpuQuota?: number;
    /** 最大内存 */
    memoryMax?: number;
  }

  interface DataMetaWorkflowPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数据源分类 */
    dbSourceType?: string;
    /** 数据源ID */
    dbSourceId?: number;
    /** 采集任务名称 */
    content?: string;
    /** 工作流实例id */
    workflowInstanceId?: number;
  }

  interface DataMissionWarehouseDto {
    /** 数据仓模块任务id */
    id?: number;
    /** 任务总线id */
    modelId?: number;
    /** 任务名称 */
    name?: string;
    /** 1.未执行。2执行中，3.执行成功，4.执行失败 */
    status?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  }

  interface DataMissionWarehousePage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数仓id */
    modelid?: number;
  }

  interface DataPreviewDO {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 可使用uuid,同样查询条件，只改变分页时不变，改变表或者查询条件时重新生成一个 */
    key?: string;
    /** 数据集id */
    id?: number;
  }

  interface DataProjectCode {
    /** DS项目projectCode */
    dataProjectCode: number;
  }

  interface DataProjectDomain {
    /** 数据主题域id */
    id?: number;
    /** 所属数据板块id */
    projectid?: number;
    /** 父级数据主题域id */
    pid?: number;
    /** 数据主题域名称 */
    name?: string;
    /** 数据主题域图标 */
    icon?: string;
    /** 数据主题域编码 */
    code?: string;
    /** 排序序号 */
    seq?: number;
    /** 负责人 */
    owner?: string;
    /** 数据主题域备注 */
    remark?: string;
    /** 创建时间 */
    createdat?: string;
    /** 主题域更新时间 */
    updatedat?: string;
    /** 删除状态0:正常;1:删除 */
    deleted?: number;
    /** 祖先节点 */
    ancestor?: string;
    /** 子主题域 */
    child?: DataProjectDomain[];
  }

  interface DataProjectDomainDto {
    /** 数据主题域id */
    id?: number;
    /** 所属数据板块id */
    projectid: number;
    /** 父级数据主题域id，默认为0 */
    pid: number;
    /** 数据主题域名称 */
    name: string;
    /** 数据主题域图标 */
    icon: string;
    /** 数据主题域编码 */
    code: string;
    /** 排序序号 */
    seq: number;
    /** 负责人 */
    owner?: string;
    /** 数据主题域备注 */
    remark?: string;
    /** 祖先节点 */
    ancestor?: string[];
  }

  interface DataProjectDomainEntityDto {
    /** 数据主题域(业务)实体id */
    id?: number;
    /** 所属数据板块id */
    projectid: number;
    /** 父级(业务)实体id */
    pid: number;
    /** 实体名称 */
    name: string;
    /** 实体编码 */
    code: string;
    /** 实体类别 */
    type: string;
    /** 实体所属的主题域id */
    domainId: number;
    /** 负责人 */
    owner: string;
    /** 数据实体备注 */
    remark?: string;
    attributes?: JsonNode;
    /** 排序序号 */
    seq?: number;
    /** 版本号 */
    version?: string;
    /** 实体关系 */
    relationList?: DataProjectDomainEntityRelationDto[];
  }

  interface DataProjectDomainEntityHistoryListVo {
    /** 数据主题域实体历史记录ids */
    ids?: number;
    /** 实体类别 */
    type?: string;
    /** 版本号 */
    version?: string;
    /** 更新时间 */
    updatedat?: string;
    /** 创建者 */
    realName?: string;
  }

  interface DataProjectDomainEntityHistoryVo {
    /** 数据主题域实体历史记录ids */
    ids?: number;
    /** 数据主题域实体id */
    id?: number;
    /** 所属数据板块id */
    projectid?: number;
    /** 父级(业务)实体id */
    pid?: number;
    /** 数据实体名称 */
    name?: string;
    /** 实体编码 */
    code?: string;
    /** 实体类别 */
    type?: string;
    /** 实体所属的主题域id */
    domainId?: number;
    /** 负责人 */
    owner?: string;
    /** 数据实体备注 */
    remark?: string;
    attributes?: JsonNode;
    /** 排序序号 */
    seq?: number;
    /** 上线状态0下线1上线 */
    isonline?: number;
    /** 版本号 */
    version?: string;
    /** 创建时间 */
    createdat?: string;
    /** 更新时间 */
    updatedat?: string;
    /** 删除状态0:正常;1:删除 */
    deleted?: number;
    /** 操作说明 */
    action?: string;
    /** 关联实体关系 */
    relationList?: DataProjectDomainEntityRelationVo[];
    /** 临时关联实体关系json字符串 */
    temrelationJson?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 创建者 */
    realName?: string;
    /** 主题域名称 */
    domainName?: string;
    /** 上级实体名称 */
    pname?: string;
  }

  interface DataProjectDomainEntityListVo {
    /** 数据主题域实体id */
    id?: number;
    /** 父级(业务)实体id */
    pid?: number;
    /** 数据实体名称 */
    name?: string;
    /** 实体编码 */
    code?: string;
    /** 所属数据板块id */
    projectid?: number;
    /** 所属数据板块名称 */
    projectName?: string;
    /** 实体所属的主题域id */
    domainId?: number;
    /** 实体所属的主题域名称 */
    domainName?: string;
    /** 实体类别 */
    type?: string;
    /** 上线状态0下线1上线 */
    isonline?: number;
    /** 关联逻辑表 */
    counttable?: number;
    /** 负责人 */
    owner?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 排序序号 */
    seq?: number;
    /** 创建时间 */
    createdat?: string;
    /** 更新时间 */
    updatedat?: string;
  }

  interface DataProjectDomainEntityPage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 所属数据板块id */
    projectid: number;
    /** 父级数据领域id，默认为0 */
    pid?: number;
    /** 数据领域名称 */
    name?: string;
  }

  interface DataProjectDomainEntityRelationDto {
    /** 数据主题域实体关系id */
    id?: number;
    /** 指向关联的实体id */
    toTableid: number;
    /** 关系名称 */
    relation?: string;
    /** 属性；字典表 */
    attribute: string;
  }

  interface DataProjectDomainEntityRelationTreeVo {
    /** 主关联的实体id */
    fromTableid?: number;
    /** 指向关联的实体id */
    toTableid?: number;
    /** 关系名称 */
    relation?: string;
    /** 属性；字典表 */
    attribute?: string;
    fromTableName?: string;
    toTableName?: string;
  }

  interface DataProjectDomainEntityRelationVo {
    /** 数据主题域实体关系id */
    id?: number;
    /** 指向关联的实体id */
    toTableid?: number;
    /** 关系名称 */
    relation?: string;
    /** 属性；字典表 */
    attribute?: string;
    /** 指向关联的实体名称 */
    toTableName?: string;
  }

  interface DataProjectDomainEntityTableDto {
    /** 所属数据领域id */
    entityId: number;
    /** 关联的数据表id */
    tableId: number;
    /** 排序序号 */
    seq?: number;
    /** 表对应数据仓中的模型id */
    modelId?: number;
  }

  interface DataProjectDomainEntityTableListVo {
    /** 数据实体关联表id */
    id?: number;
    /** 逻辑表名 */
    tableName?: string;
    /** 负责人 */
    owner?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 主题域名称id */
    domainId?: number;
    /** 主题域名称 */
    domainName?: string;
    /** 删除状态0:正常;1:删除 */
    deleted?: number;
  }

  interface DataProjectDomainEntityTablePage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 业务实体id */
    entityId: number;
  }

  interface DataProjectDomainEntityVerisonPage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 数据主题域实体id */
    id: number;
    /** 版本号 */
    version?: string;
  }

  interface DataProjectDomainEntityVo {
    /** 数据主题域实体id */
    id?: number;
    /** 所属数据板块id */
    projectid?: number;
    /** 父级(业务)实体id */
    pid?: number;
    /** 数据实体名称 */
    name?: string;
    /** 实体编码 */
    code?: string;
    /** 实体类别 */
    type?: string;
    /** 实体所属的主题域id */
    domainId?: number;
    /** 负责人 */
    owner?: string;
    /** 数据实体备注 */
    remark?: string;
    attributes?: JsonNode;
    /** 排序序号 */
    seq?: number;
    /** 版本号 */
    version?: string;
    /** 关联实体关系 */
    relationList?: DataProjectDomainEntityRelationDto[];
  }

  interface DataProjectDomainForDs {
    /** 主题域id */
    id?: number;
    /** 数据主题域名称 */
    name?: string;
  }

  interface DataProjectDomainPage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 所属数据板块id */
    projectid?: number;
    /** 父级数据主题域id，默认为0 */
    pid?: number;
    /** 数据主题域名称 */
    name?: string;
  }

  interface DataProjectDomainTableDetailVo {
    /** 资产数据表id */
    id?: number;
    /** 数据库真实名称 */
    tableCode?: string;
    /** 平台写的中文名称 */
    tableName?: string;
    /** 版本号 */
    version?: string;
    /** 涉密等级 */
    securityLevel?: string;
    /** 涉密等级名称 */
    securityLevelName?: string;
    /** 对象描述 */
    description?: string;
    /** 创建时间 */
    createdat?: string;
    /** 生命周期单位(年 |月| 周 |日| 永久) */
    cycleUnit?: string;
    /** 生命生命周期时长 */
    cycleDuration?: number;
    /** 所属数据板块id */
    projectid?: number;
    /** 实体所属的主题域id */
    domainId?: number;
    /** 负责人 */
    owner?: string;
    /** 对应数据源id */
    dbSourceId?: number;
    /** 所属数据板块名称 */
    projectName?: string;
    /** 实体所属的主题域名称 */
    domainName?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 对应数据源名称 */
    dbSourceName?: string;
    /** 创建人 */
    creater?: string;
    /** 表记录总行数 */
    rowCount?: number;
    /** 数据量，单位kb */
    dataSize?: number;
    /** ddl语句 */
    ddl?: string;
    /** flink ddl语句 */
    flinkDdl?: string;
    /** 表类型: table | view */
    tableType?: string;
    tableColumns?: JsonNode;
    tableConstraints?: JsonNode;
    /** 建表语句 */
    tableSql?: string;
    tableLineage?: JsonNode;
  }

  interface DataProjectDomainTableListVo {
    /** 资产数据表id */
    id?: number;
    /** 数据库真实名称 */
    tableCode?: string;
    /** 平台写的中文名称 */
    tableName?: string;
    /** 版本号 */
    version?: string;
    /** 涉密等级 */
    securityLevel?: string;
    /** 涉密等级名称 */
    securityLevelName?: string;
    /** 对象描述 */
    description?: string;
    /** 所属数据板块id */
    projectid?: number;
    /** 所属数据板块名称 */
    projectName?: string;
    /** 实体所属的主题域id */
    domainId?: number;
    /** 实体所属的主题域名称 */
    domainName?: string;
    /** 负责人 */
    owner?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 对应数据源id */
    dbSourceId?: number;
    /** 对应数据源名称 */
    dbSourceName?: string;
    /** 访问 */
    visit?: number;
    /** 编辑 */
    edit?: number;
    /** 开放 */
    open?: number;
    /** 审计 */
    audit?: number;
  }

  interface DataProjectDomainTablePage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 负责人 */
    owner?: string;
    /** 所属数据板块id */
    projectid?: number;
    /** 表所属主题域id */
    domainId?: number;
    /** 输入内容 */
    param?: string;
  }

  interface DataProjectDomainTableSimpleListVo {
    /** 资产数据表id */
    id?: number;
    /** 数据库真实名称 */
    tableName?: string;
    /** 数仓模型id */
    warehouseModelId?: number;
  }

  interface DataProjectDomainVo {
    /** id */
    id?: number;
    /** 所属数据板块id */
    projectid?: number;
    /** 父级数据主题域id */
    pid?: number;
    /** 数据主题域名称 */
    name?: string;
    /** 数据主题域图标 */
    icon?: string;
    /** 数据主题域编码 */
    code?: string;
    /** 排序序号 */
    seq?: number;
    /** 数据主题域备注 */
    remark?: string;
    /** 负责人 */
    owner?: string;
    /** 负责人名称 */
    ownerName?: string;
    /** 关联实体关系 */
    ancestor?: string[];
  }

  interface DataProjectDto {
    /** id */
    id?: number;
    /** 数据板块中文名称 */
    zhName: string;
    /** 数据板块英文名称 */
    enName: string;
    /** 板块图标 */
    icon: string;
    /** 数据板块备注 */
    remark?: string;
    /** 数据板块编码 */
    code?: string;
    /** 业务负责人 */
    businessOwner: string[];
    /** 数据负责人 */
    dataOwner: string;
    /** 数据管理者 */
    adminOwner: string[];
  }

  interface DataProjectVo {
    /** id */
    id?: number;
    /** 数据板块中文名称 */
    zhName?: string;
    /** 数据板块英文名称 */
    enName?: string;
    /** 板块图标 */
    icon?: string;
    /** 数据板块备注 */
    remark?: string;
    /** 数据板块编码 */
    code?: string;
    /** 业务负责人 */
    businessOwner?: string[];
    /** 数据负责人 */
    dataOwner?: string;
    /** 数据管理者 */
    adminOwner?: string[];
    /** 主题域数量 */
    domainCount?: number;
    /** 业务实体数量 */
    entityCount?: number;
  }

  interface DataQualityTaskDto {
    /** id */
    id?: number;
    /** 任务名称 */
    taskName: string;
    /** 数据板块id */
    dataProjectId: number;
    /** 表所属主题域id */
    domainId: number;
    /** 表所属主题域全路径id */
    domainFullPathId?: string;
    /** 数据表id */
    tableId: number;
    /** 表名 */
    tableName?: string;
    /** 规则列表 */
    ruleList: DataQualityTaskRuleDto[];
    taskPlan?: DataQualityTaskPlanDto;
  }

  interface DataQualityTaskListVo {
    /** id */
    id?: number;
    /** 报告id */
    reportId?: number;
    /** 任务实例的id */
    instanceId?: string;
    /** 任务名称 */
    taskName?: string;
    /** 主题域 */
    domainName?: string;
    /** 数据表名称 */
    tableName?: string;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface DataQualityTaskPlanDto {
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 任务间隔 */
    delay?: number;
    /** 延迟时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每日（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 执行次数 */
    runCount?: number;
  }

  interface DataQualityTaskQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 状态 1成功 2失败 */
    status?: number;
    /** 执行时间 */
    executeTime?: string[];
    /** 主题域id */
    domainId?: number;
    /** 任务实例的id */
    instanceId?: string;
  }

  interface DataQualityTaskResultReport {
    id?: number;
    /** 任务id */
    taskId?: number;
    /** 任务实例id */
    instanceId?: string;
    /** 表所属主题域id */
    domainId?: number;
    /** 表所属主题域名称 */
    domainName?: string;
    /** 表id */
    tableId?: number;
    /** 表名 */
    tableName?: string;
    /** 检测开始时间 */
    checkStartTime?: string;
    /** 检测结束时间 */
    checkEndTime?: string;
    /** 综合得分 */
    totalScore?: string;
    /** 唯一性质量评分 */
    ruleUniqueScore?: string;
    /** 唯一性详情 */
    ruleUniqueDetail?: string;
    /** 准确性质量评分 */
    ruleAccuracyScore?: string;
    /** 准确性详情 */
    ruleAccuracyDetail?: string;
    /** 有效性质量评分 */
    ruleValidityScore?: string;
    /** 有效性详情 */
    ruleValidityDetail?: string;
    /** 一致性质量评分 */
    ruleConsistencyScore?: string;
    /** 一致性详情 */
    ruleConsistencyDetail?: string;
    /** 完整性质量评分 */
    ruleIntegrityScore?: string;
    /** 完整性详情 */
    ruleIntegrityDetail?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  }

  interface DataQualityTaskRuleDto {
    /** 规则排序序号 */
    ruleSeq?: number;
    /** 规则编码 */
    ruleCode: string;
    /** 检测字段 */
    checkCol: string;
    /** 得分权重 */
    weight: number;
    /** 运算符 */
    operator?: string;
    /** 预期值 */
    expectedValue?: string;
    /** 正则表达式 */
    regPattern?: string;
    /** 比较字段 */
    compareCol?: string;
    /** 比较表所属的主题域id */
    compareDomainId?: number;
    /** 比较表所属的主题域全链路id */
    domainFullPathId?: string;
    /** 比较表的id */
    compareTableId?: number;
    /** 比较表名 */
    compareTableName?: string;
    /** 两表关联条件 */
    joinCondition?: string;
  }

  interface DataQueueDto {
    /** id */
    id?: number;
    /** 名称 */
    name: string;
    /** 资产描述 */
    description?: string;
    /** 对应数据源id */
    dbSourceId: number;
    /** 所属数据板块id */
    projectid: number;
    /** 所属主题域id */
    domainId: number;
    /** 字段信息 */
    tableColumns?: QueueColumn[];
    /** 负责人 */
    owner: string;
  }

  interface DataQueueListVo {
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 资产描述 */
    description?: string;
    /** 所属数据板块id */
    projectid?: number;
    /** 所属数据板块名称 */
    projectName?: string;
    /** 所属的主题域id */
    domainId?: number;
    /** 所属的主题域名称 */
    domainName?: string;
    /** 管理员 */
    owner?: string;
    /** 管理员名称 */
    ownerName?: string;
    /** 访问 */
    visit?: number;
    /** 编辑 */
    edit?: number;
    /** 开放 */
    open?: number;
    /** 审计 */
    audit?: number;
    /** 被使用次数 */
    usedCount?: number;
  }

  interface DataQueuePage {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    /** 所属数据板块id */
    projectid?: number;
    /** 所属主题域id */
    domainId?: number;
    /** 输入内容 */
    param?: string;
  }

  interface DataQueueSimpleListVo {
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
  }

  interface DataSecurityKeyDto {
    /** 唯一标识符 */
    id?: number;
    /** 名称 */
    name: string;
    /** 加密方式ID */
    encryptionMethodId: number;
    /** 描述 */
    description?: string;
    /** 生命周期 */
    lifecycle: number;
    /** 私钥 */
    privateKey?: string;
    /** 公钥 */
    publicKey?: string;
    /** 私钥密码 */
    keyPassword?: string;
    /** 密钥长度 */
    keyBits?: number;
  }

  interface DataSecurityKeyListVo {
    /** 唯一标识符 */
    id?: number;
    /** 名称 */
    name?: string;
    /** 私钥 */
    privateKey?: string;
    /** 公钥 */
    publicKey?: string;
    /** 描述 */
    description?: string;
    /** 状态 0-关闭 1-开启 */
    status?: number;
    /** 生命周期 */
    lifecycle?: number;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface DataSecurityKeyQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 加密方式ID */
    encryptionMethodId?: number;
    /** 名称 */
    name?: string;
  }

  interface DataSecurityScanTaskDto {
    /** id */
    id?: number;
    /** 任务名称 */
    taskName: string;
    /** 数据板块id */
    dataProjectId: number;
    /** 表所属主题域id */
    domainId: number;
    /** 表所属主题域全路径id */
    domainFullPathId?: string;
    /** 数据表id */
    tableId: string;
    /** 任务描述 */
    taskDesc?: string;
    /** 敏感标签id */
    sensitiveLabelId: number;
    /** 脱敏模板id */
    desensitizationTemplateId: number;
    taskPlan: DataSecurityTaskPlanDto;
  }

  interface DataSecurityScanTaskQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 主题域id */
    domainId?: number;
    /** 实例id */
    instanceId?: string;
  }

  interface DataSecurityTaskPlanDto {
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 任务间隔 */
    delay?: number;
    /** 延迟时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每日（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 执行次数 */
    runCount?: number;
  }

  interface DataSortPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 父id */
    pid?: number;
  }

  interface DataSource {
    /** id */
    id?: number;
    /** 数据源类型 */
    sourceType?: string;
    /** 数据源名称 */
    name?: string;
    /** 描述 */
    description?: string;
    /** 数据库名称 */
    databaseName?: string;
    /** 数据库连接ip地址 */
    ip?: string;
    /** 数据库连接端口 */
    port?: string;
    /** 数据库访问用户名 */
    username?: string;
    /** 数据库访问密码 */
    password?: string;
    /** 数据源创建时间 */
    createTime?: string;
    /** 数据源更新时间 */
    updateTime?: string;
    /** 删除状态0:正常;1:删除 */
    deleted?: number;
    /** DS的数据源id */
    dsDataSource?: number;
    /** 链接状态0:未链接;1:链接成功;2-链接失败 */
    linkStatus?: number;
    /** 板块id */
    dataProjectId?: number;
    /** 模式名称 */
    schemaName?: string;
    /** hive-conf-dir */
    hiveConfDir?: string;
    /** hudi-parh */
    hudiParh?: string;
    /** hudi-metastore-uris */
    hudiMetastoreUris?: string;
    /** doris查询端口 */
    dorisPort?: string;
    /** 协议类型 JDBC  |  Thrift ( hive )  | TCP （kafka）| API （hudi） */
    protocol?: string;
  }

  interface DataSourceAddDto {
    /** 数据源类型 */
    sourceType: string;
    /** 数据源名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 数据库名称 */
    databaseName?: string;
    /** 数据库连接ip地址 */
    ip?: string;
    /** 数据库连接端口 */
    port?: string;
    /** 数据库访问用户名 */
    username?: string;
    /** 数据库访问密码 */
    password?: string;
    /** 板块id */
    dataProjectId?: number;
    /** 模式名称 */
    schemaName?: string;
    /** hive-conf-dir */
    hiveConfDir?: string;
    /** hudi-parh */
    hudiParh?: string;
    /** hudi-metastore-uris */
    hudiMetastoreUris?: string;
    /** doris查询端口 */
    dorisPort?: string;
    /** 协议类型 JDBC  |  Thrift ( hive )  | TCP （kafka）| API （hudi） */
    protocol?: string;
  }

  interface DataSourceEditDto {
    /** id */
    id: number;
    /** 数据源类型 */
    sourceType: string;
    /** 数据源名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 数据库名称 */
    databaseName?: string;
    /** 数据库连接ip地址 */
    ip?: string;
    /** 数据库连接端口 */
    port?: string;
    /** 数据库访问用户名 */
    username?: string;
    /** 数据库访问密码 */
    password?: string;
    /** 板块id */
    dataProjectId?: number;
    /** 模式名称 */
    schemaName?: string;
    /** hive-conf-dir */
    hiveConfDir?: string;
    /** hudi-parh */
    hudiParh?: string;
    /** hudi-metastore-uris */
    hudiMetastoreUris?: string;
    /** doris查询端口 */
    dorisPort?: string;
    /** 协议类型 JDBC  |  Thrift ( hive )  | TCP （kafka）| API （hudi） */
    protocol?: string;
  }

  interface DataSourcePageDto {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数据源类型 */
    sourceType?: string;
    /** 数据源名称 */
    name?: string;
    /** 板块id */
    dataProjectId: number;
  }

  interface DataSourcePageVo {
    /** id */
    id?: number;
    /** 数据源类型 */
    sourceType?: string;
    /** 数据源名称 */
    name?: string;
    /** 数据库连接ip地址 */
    ip?: string;
    /** 数据库连接端口 */
    port?: string;
    /** 数据库访问用户名 */
    username?: string;
    /** 链接状态0:未链接;1:链接成功;2-链接失败 */
    linkStatus?: number;
    /** 数据库名称 */
    databaseName?: string;
    /** 数据库访问密码 */
    password?: string;
    /** 模式名称 */
    schemaName?: string;
    /** hive-conf-dir */
    hiveConfDir?: string;
    /** hudi-parh */
    hudiParh?: string;
    /** hudi-metastore-uris */
    hudiMetastoreUris?: string;
    /** 协议类型 JDBC  |  Thrift ( hive )  | TCP （kafka）| API （hudi） */
    protocol?: string;
  }

  interface DataSourceSimpleDto {
    /** 数据源id */
    id?: number;
    /** 数据源名称 */
    name?: string;
  }

  interface DataSourceTestDto {
    /** id */
    id?: number;
    /** 数据源类型 */
    sourceType: string;
    /** 数据源名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 数据库名称 */
    databaseName: string;
    /** 数据库连接ip地址 */
    ip?: string;
    /** 数据库连接端口 */
    port?: string;
    /** 数据库访问用户名 */
    username?: string;
    /** 数据库访问密码 */
    password?: string;
    /** 板块id */
    dataProjectId?: number;
    /** 模式名称 */
    schemaName?: string;
    /** hive-conf-dir */
    hiveConfDir?: string;
    /** hudi-parh */
    hudiParh?: string;
    /** hudi-metastore-uris */
    hudiMetastoreUris?: string;
  }

  interface DataSourceType {
    /** 数据库类型 */
    type?: string;
    /** 数据库类型名称 */
    name?: string;
    /** 排序 */
    sort?: number;
  }

  interface DataStandardDictListVo {
    id?: number;
    /** 字典名称 */
    dictName?: string;
    /** 英文名称 */
    enName?: string;
    /** 版本号 */
    version?: string;
    /** 备注 */
    remark?: string;
    /** 提交时间 */
    updatedAt?: string;
    /** 状态 -1:草稿 0:下线 1:上线 */
    status?: number;
  }

  interface DataWarehouseLayerDto {
    id?: number;
    /** 分层名称 */
    name?: string;
    /** 分层编码 */
    code?: string;
    /** 分层说明 */
    remark?: string;
    /** 排序序号 */
    seq?: number;
    /** 添加时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 分类编码 */
    typeCode?: string;
    /** 二级分类编码 */
    secondTypeCode?: string;
    /** 分类名称 */
    typeCodeName?: string;
    /** 二级分类名称 */
    secondTypeCodeName?: string;
  }

  interface DataWarehouseLayerMap {
    /** 分类编码 */
    typeCode?: string;
    /** 分类名称 */
    typeCodeName?: string;
    /** 数仓分层列表 */
    list?: DataWarehouseLayerDto[];
  }

  interface DataWarehouseLayerSave {
    /** id,新增时为空, 编辑时必填 */
    id?: number;
    /** 分类编码 */
    typeCode: string;
    /** 二级分类编码 */
    secondTypeCode: string;
    /** 分层名称 */
    name: string;
    /** 分层编码 */
    code: string;
    /** 分层说明 */
    remark?: string;
    /** 排序序号 */
    seq: number;
  }

  interface DataWarehouseModel {
    id?: number;
    /** 所属分层id */
    layerid?: number;
    /** 模型对应表所属数据源id */
    dbSourceId?: number;
    /** 模型名称 */
    modelName?: string;
    /** 模型表名：tabelPrefix+'_'+tabelNameTemp */
    tableName?: string;
    /** 表前中缀 */
    tablePrefix?: number;
    /** 新建表名 */
    tableNameTemp?: string;
    /** 创建模型方式:0:仅仅创建模型 1：创建模型及物理表 2：已有物理表仅创建模型。 */
    createType?: number;
    /** 创建者 */
    createBy?: string;
    /** 数据来源类别 */
    recordFrom?: number;
    /** 数据来源关联id */
    recordReferId?: number;
    /** 备注 */
    remark?: string;
    /** 1：正常；0：删除,2:草稿 */
    status?: number;
    /** 添加时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 发布状态，0：未发布，1：已发布，2：可以发布（只有当前状态下可以发布）,3:正在发布 */
    publishStatus?: number;
    /** 所有字段列表 */
    columns?: DataWarehouseModelColumns[];
    /** 所有索引列表 */
    indexs?: DataWarehouseModelIndex[];
  }

  interface DataWarehouseModelColumns {
    id?: number;
    /** 所属模型id */
    modelid?: number;
    /** 字段逻辑名 */
    name?: string;
    /** 字段名 */
    colname?: string;
    /** 字段所属flink类型 */
    colFlinkType?: string;
    /** 字段数据库类型 */
    coltype?: string;
    /** 字段长度 */
    length?: number;
    /** 小数点 */
    decLength?: number;
    /** 说明 */
    comment?: string;
    /** 是否主键, 1:是, 0：否 */
    pk?: number;
    /** 是否自增, 1:是, 0：否 */
    autoinc?: number;
    /** 是否为空, 1:是, 0：否 */
    notNull?: number;
    /** 缺省值 */
    defaultValue?: string;
    /** 状态（前端不传）1：正常；0：删除,2：添加失败 */
    status?: number;
    /** 添加时间,前端不传 */
    createTime?: string;
    /** 修改时间,前端不传 */
    updateTime?: string;
    /** 是否分区，1:是, 0：否;数据源类型是Hive,Hudi,Doris使用 */
    partition?: number;
    /** 分桶数量，>0 ;数据源类型是Hive和Doris使用 */
    buckets?: number;
    /** 去重字段，1:是, 0：否 ;数据源类型是Hudi使用 */
    precombine?: number;
  }

  interface DataWarehouseModelIndex {
    id?: number;
    /** 所属模型id */
    modelid?: number;
    /** 索引名称值不用传，后台自动生成 */
    name?: string;
    /** 表字段名, 多个用英文逗号分隔 */
    columnNames: string;
    /** 索引类型 ：UNIQUE，空值, */
    type?: string;
    /** 1：正常；0：删除 2：操作失败 */
    status?: number;
    createTime?: string;
    updateTime?: string;
  }

  interface DataWarehouseModelPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数仓分类编码 */
    typeCode?: string;
    /** 数仓分层id */
    layerid?: number;
    /** 内容搜索，可搜索模型名称和模型表名 */
    content?: string;
  }

  interface DataWarehouseModelSave {
    id?: number;
    /** 所属分层id */
    layerid?: number;
    /** 模型对应表所属数据源id */
    dbSourceId?: number;
    /** 模型名称 */
    modelName?: string;
    /** 表前中缀，传id */
    tablePrefix?: number;
    /** 新建表名 */
    tableNameTemp?: string;
    /** 创建模型方式:0:仅仅创建模型 1：创建模型及物理表 2：已有物理表仅创建模型。 */
    createType?: number;
    /** 1：正式；2:草稿 */
    status?: number;
    /** 备注 */
    remark?: string;
    /** 模型表的字段详情列表,当createTableType为1时有值 */
    columns?: DataWarehouseModelColumns[];
    /** 模型表的索引列表,当createTableType为1时有值(非必填) */
    indexs?: DataWarehouseModelIndex[];
    /** 表名(前缀和新建表名的组合) */
    tableName?: string;
  }

  interface DataWarehouseModelSimpleDto {
    /** id */
    id?: number;
    /** 模型名称 */
    modelName?: string;
    /** 表名 */
    tableName?: string;
    /** 数仓分层 */
    layerName?: string;
    /** 创建模型方式:仅仅创建模型, 创建模型及物理表, 已有物理表仅创建模型。 */
    createTypeInfo?: string;
    /** 1：正常；2:草稿 */
    statusInfo?: number;
    /** 添加时间 */
    createTime?: string;
    /** 发布状态，0：未发布，1：已发布，2：可以发布（只有当前状态下可以发布）,3:正在发布 */
    publishStatus?: number;
  }

  interface DataWarehouseRule {
    id?: number;
    /** 所属分层id */
    layerid?: number;
    /** 表前缀 */
    tableprefix?: string;
    /** 表中缀 */
    tablenfix?: string;
    /** 1：正常；0：删除 */
    status?: number;
    /** 添加时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
  }

  interface DataWarehouseRuleSave {
    /** id,新增时为空, 编辑时必填 */
    id?: number;
    /** 所属分层id */
    layerid?: number;
    /** 表前缀 */
    tableprefix?: string;
    /** 表中缀 */
    tablenfix?: string;
    /** 1：正常；0：删除 */
    status?: number;
  }

  interface DataWorkflowDefinePage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数据板块id */
    projectid?: number;
    /** 主题域模型id */
    projectDomainId?: number;
    /** 内容搜索，工作流名称 */
    content?: string;
  }

  interface DataWorkflowDefineRow {
    /** id */
    id?: number;
    /** 工作流名称 */
    name?: string;
    /** DS对应流程定义版本号 */
    dsProcessDefineVersion?: number;
    /** 添加时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 状态：1:上线，0:下线 */
    status?: number;
    /** ds项目编码 */
    dsProjectCode?: number;
    /** 运行状态：1:未运行，2:正在启动，3:运行中，4:正在停止 */
    runStatus?: number;
  }

  interface DataWorkflowDefineSave {
    /** 工作流id，编辑时需要传递，新增时不需要传递 */
    id?: number;
    /** 工作流类型：data_develop（数据开发）,data_meta（元数据采集） */
    type: string;
    /** DS项目编码 */
    dsProjectCode: number;
    workflowInfo: JsonNode;
  }

  interface DataWorkflowId {
    /** 工作流id */
    id: number;
  }

  interface DataWorkflowIncrTaskDto {
    /** id,新增时为空，编辑修改时必填 */
    id?: number;
    /** 工作流ID,新增时为空，编辑修改时必填 */
    dataWorkflowId?: number;
    /** 任务名称 */
    name: string;
    /** 虚拟表名 */
    virtualTableName: string;
    /** 来源数据列表 */
    sourceList?: DataWorkflowIncrTaskSourceTableDto[];
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 数据处理批次大小 */
    batchSize: number;
    /** SQL数据集语句 */
    sqlData: string;
    /** SQL插入语句 */
    sqlInsert: string;
    /** 增量同步字段 */
    increaseColumn: string;
    /** 增量同步初始值 */
    increaseValue: string;
    /** 增量类型 */
    increaseType?: number;
    /** 目标表定义主键字段 */
    keyColumn?: string;
  }

  interface DataWorkflowIncrTaskSourceTableDto {
    /** 来源数据板块ID */
    sourceProjectId?: number;
    /** 来源主题域id */
    sourceDomainId?: string;
    /** 来源表id */
    sourceDomainTableId?: number;
    /** 来源表-表名 */
    sourceTableName?: string;
  }

  interface DataWorkflowInstance {
    id?: number;
    /** 分区名，使用ds项目名称 */
    dbPartitionName?: string;
    /** 流程定义id */
    workflowDefineId?: number;
    /** DS对应流程实例id */
    dsProcessInstanceId?: number;
    /** 运行状态：SUCCESS:成功,FAILURE:失败,RUNNING:运行中 */
    status?: string;
    /** 流程运行开始时间 */
    startTime?: string;
    /** 流程运行结束时间 */
    endTime?: string;
    /** 删除状态0:正常;1:删除 */
    deleted?: number;
    /** 流程实例名称 */
    name?: string;
    /** 调度时间 */
    scheduleTime?: string;
    /** 运行类型：START_PROCESS（启动工作流） */
    commandType?: string;
    /** ds项目编码 */
    dsProjectCode?: number;
    /** DS对应流程定义code，对应ds中的workflow的id */
    dsProcessDefineCode?: number;
    /** 运行时长 */
    duration?: string;
    /** 主机 */
    host?: string;
    /** 运行批次号 */
    batchNo?: string;
    /** 任务名称 */
    taskName?: string;
  }

  interface DataWorkflowInstancePage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 工作流id */
    workflowDefineId: number;
  }

  interface DataWorkflowPlanDto {
    /** 工作流id */
    workflowDefineId: number;
    /** 任务类型(1:单次任务，2:间隔循环任务,3:指定时间循环) */
    type: number;
    /** 任务间隔 */
    delay?: number;
    /** 延迟时间单位(秒，分钟，小时，天) */
    delayUnit?: string;
    /** 指定类型周期(每天，每周，每月，每年) */
    loopType?: string;
    /** 指定分钟(指定分钟0~59) */
    loopMinute?: number;
    /** 指定小时(0~23 默认0) */
    loopHour?: number;
    /** 指定每日（每月几日0~31) */
    loopDay?: number;
    /** 指定每周(周日~周六,1~7;周日1,周一2,周二3...周六7) */
    loopWeak?: number;
    /** 指定每月(1月份~12月) */
    loopMonth?: number;
    /** 执行次数(默认1) */
    runCount?: number;
  }

  interface DbSourceAndTables {
    /** 数据源id */
    dbSourceId?: number;
    /** 数据表id列表 */
    tableIds?: number[];
  }

  interface DesensitizationTemplateDto {
    /** id */
    id?: number;
    /** 名称 */
    name: string;
    /** 敏感标签ID */
    sensitiveLabelId: number;
    /** 安全密级ID */
    securityLevelId: number;
    /** 脱敏方式：10-HASH, 20-保留前n后m, 21-保留前n至后m, 22-遮盖前n后m, 23-遮盖前n至后m */
    desensitizationMethod: number;
    /** 脱敏模块 */
    desensitizationModuleList: string[];
    /** 算法ID */
    algorithmId?: number;
    mvalue?: number;
    nvalue?: number;
  }

  interface DesensitizationTemplateListVo {
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 状态 0-关闭 1-开启 */
    status?: number;
    /** 安全密级 */
    level?: string;
    /** 脱敏方式 */
    method?: string;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface DesensitizationTemplateQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
  }

  interface DetectionResultVo {
    /** id */
    id?: number;
    /** 数据板块 */
    dataProjectName?: string;
    /** 主题域 */
    domainName?: string;
    /** 数据表 */
    tableName?: string;
    /** 数据表id */
    tableId?: number;
    /** 字段名 */
    columnName?: string;
    /** 敏感标签 */
    sensitiveLabel?: string;
    /** 已添加到动态脱敏 */
    added?: boolean;
    /** 识别方式：内容识别，名称识别 */
    identificationMethod?: string;
  }

  interface DictData {
    id?: number;
    /** 条目代码 */
    dataCode?: string;
    /** 条目名称 */
    dataName?: string;
    /** 排序 */
    sort?: number;
    /** 大分类名称，查询主要参数 */
    dictCode?: string;
    /** 描述 */
    remark?: string;
    /** 1:启用, 0:删除, 2：禁用 */
    status?: number;
    /** 父条目代码，默认为null，对于有父子结构的条目有用 */
    parentDataCode?: string;
    /** 条目名称1 */
    dataName1?: string;
  }

  interface DictDataColumnType {
    id?: number;
    /** 类型名称 */
    name?: string;
    /** flink类型名称 */
    flinkType?: string;
    /** MySQL类型名称 */
    mysqlType?: string;
    /** pgsql类型名称 */
    pgsqlType?: string;
    /** Hive类型名称 */
    hiveType?: string;
    /** Hudi类型名称 */
    hudiType?: string;
    /** doris类型名称 */
    dorisType?: string;
  }

  interface DictDataParams {
    /** 字典条目分类 */
    dictCode: string;
    /** 父条目编码，非必填，有父子关系并查询子集数据时使用 */
    parentDataCode?: string;
  }

  interface DictionaryDto {
    /** id */
    id?: number;
    /** 字典名称 */
    dictName: string;
    /** 英文名称 */
    enName: string;
    /** 分类目录id */
    directoryId: number;
    /** 分类目录 */
    directory?: number;
    /** 分类目录节点类型 */
    directoryNodeType: number;
    /** 版本号 */
    version?: string;
    /** 负责人ID */
    personInChargeId?: string;
    /** 生命周期 */
    lifecycle: number;
    /** 创建时间 */
    createdAt?: string;
    /** 修改时间 */
    updatedAt?: string;
    /** 创建人 */
    creator?: string;
    /** 状态 0:下线 1:上线 */
    status?: number;
    /** 字典项列表 */
    itemList?: DictionaryItemDto[];
    /** 备注 */
    remark?: string;
  }

  interface DictionaryItemDto {
    /** 代码 */
    code: string;
    /** 代码描述 */
    codeDescription: string;
  }

  interface DictionaryQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 目录id */
    directoryId: number;
    /** 目录节点类型 */
    directoryNodeType: number;
    /** 状态 */
    status?: number;
    /** 中英文名称 */
    name?: string;
  }

  interface DirectoryDto {
    /** id */
    id?: number;
    /** 父级id */
    pid: number;
    /** 名称 */
    name: string;
    /** 节点类型 0-不可编辑(数据板块) 1-不可编辑(主题域) 2-可编辑(目录节点) 3-字典表/数据元 */
    nodeType?: number;
    /** 父节点类型 0-不可编辑(数据板块) 1-不可编辑(主题域) 2-可编辑(目录节点) */
    parentNodeType: number;
    /** 目录类型 1-数据元管理 2-字典表管理 */
    type?: number;
    /** 排序序号 */
    seq?: number;
    /** 子节点 */
    children?: DirectoryDto[];
    /** 唯一标识(该目录树由多块数据组成,故id不唯一) */
    uniqueMark?: string;
    /** 父节点唯一标识(该目录树由多块数据组成,故id不唯一) */
    parentUniqueMark?: string;
    /** 主题域id */
    domainId: number;
    display?: boolean;
  }

  interface DiscStatusStatVo {
    /** code */
    code?: number;
    /** 名称 */
    name?: string;
    /** 数量 */
    count?: number;
  }

  interface DsDataSourceSimple {
    /** ds数据源id */
    id?: number;
    /** ds数据源名称 */
    name?: string;
  }

  interface DsDataSourceType {
    /** 数据源类型 */
    type: string;
  }

  interface DsTaskInstance {
    /** 任务实例id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 节点类型 */
    taskType?: string;
    /** 状态 */
    state?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 运行时间 */
    duration?: string;
    /** 主机 */
    host?: string;
  }

  interface DsTaskInstancePage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 工作流实例id */
    workflowInstanceId?: number;
  }

  interface DsTaskRunLogParams {
    /** 任务实例id */
    taskInstanceId: number;
    /** 获取日志行数，默认100行 */
    limitLineNum?: number;
    /** 忽略行数，默认0 */
    skipLineNum?: number;
  }

  interface DsWorkerGroup {
    name?: string;
  }

  interface DynamicMaskRuleDto {
    /** id */
    id?: number;
    /** 名称 */
    name: string;
    /** 敏感标签ID */
    sensitiveLabelId: number;
    /** 安全密级ID */
    securityLevelId: number;
    /** 脱敏方式：10-HASH, 20-保留前n后m, 21-保留前n至后m, 22-遮盖前n后m, 23-遮盖前n至后m */
    desensitizationMethod: number;
    /** 脱敏模块 */
    desensitizationModuleList: string[];
    /** 算法ID */
    algorithmId?: number;
    mvalue?: number;
    nvalue?: number;
  }

  interface DynamicMaskRuleListVo {
    /** id */
    id?: number;
    /** 数据板块 */
    dataProjectName?: string;
    /** 主题域 */
    domainName?: string;
    /** 数据表 */
    tableName?: string;
    /** 字段名 */
    columnName?: string;
    /** 敏感标签 */
    sensitiveLabel?: string;
    /** 脱敏方式 */
    method?: string;
    /** 状态 0-关闭 1-开启 */
    status?: number;
  }

  interface DynamicMaskRuleQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 主题域id */
    domainId?: number;
    /** 敏感标签ID */
    sensitiveLabelId?: number;
    /** 脱敏方式：1-HASH加密, 2-掩盖 */
    method?: string;
  }

  interface EdusoaAppUserDO {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    name?: string;
    role?: string;
  }

  interface EdusoaAppUserDto {
    id?: number;
    username?: string;
    password?: string;
    name: string;
    phone: string;
    company?: string;
    role: string;
    status?: number;
    clientId?: string;
    clientSecret?: string;
  }

  interface EncryptInfo {
    /** 私钥 */
    privateKey?: string;
    /** 公钥 */
    publicKey?: string;
    /** 私钥密码 */
    privateKeyPassword?: string;
    /** 加密方式(算法) */
    algorithm?: string;
    /** 盐值 */
    salt?: string;
  }

  interface EncryptionAlgorithmDto {
    /** id */
    id?: number;
    /** 名称 */
    name: string;
    /** 加密算法ID */
    encryptionMethodId: number;
    /** 加密类路径 */
    encryptionClassPath?: string;
    /** 密钥ID */
    keyId?: number;
    /** 盐值 */
    salt?: string;
    /** 加密方式 */
    method?: string;
  }

  interface EncryptionAlgorithmListVo {
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 加密类型 */
    type?: string;
    /** 加密算法 */
    algorithmName?: string;
    /** 加密/解密 api */
    encryptionClassPath?: string;
    /** 解密密钥 */
    privateKey?: string;
    /** 状态 0-关闭 1-开启 */
    status?: number;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface EncryptionAlgorithmNewListVo {
    /** id */
    id?: number;
    /** 名称 */
    name?: string;
    /** 加密类型 */
    type?: string;
    /** 加密算法 */
    algorithmName?: string;
  }

  interface EncryptionAlgorithmQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 类型code 1-对称加密 2-非对称加密 3-摘要算法 */
    typeCode?: number;
  }

  interface EncryptionMethodVo {
    /** 唯一标识符 */
    id?: number;
    /** 加密方式名称 */
    name?: string;
    /** 类型 */
    type?: string;
    /** 类型code 1-对称加密 2-非对称加密 3-摘要算法 */
    typeCode?: number;
    /** 描述 */
    description?: string;
    /** 加密类路径 */
    encryptionClassPath?: string;
    /** 密钥支持位数 */
    keyBits?: number[];
    /** 支持算法 */
    algorithms?: string[];
    /** 子集 */
    children?: EncryptionMethodVo[];
  }

  interface EncryptTaskDto {
    /** id,新增时为空 */
    id?: number;
    /** 数据板块id */
    dataProjectId: number;
    /** 主题域id列表 */
    projectDomainIds: string;
    /** 数据资产id */
    domainTableId: number;
    /** 工作流ID,新增时为空，编辑修改时必填 */
    dataWorkflowId?: number;
  }

  interface EncryptTaskId {
    /** 加密任务节点配置id,保存接口返回的encryptTaskId */
    encryptTaskId: number;
  }

  interface EncryptTaskScript {
    /** id,新增时为空 */
    encryptTaskId?: number;
    /** 脚本 */
    rawScript?: string;
  }

  interface ExcelSetEditDO {
    /** excel数据集id */
    id?: number;
    /** 任务名称 */
    name: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 应用说明--备注 */
    remark?: string;
  }

  interface ExcelSetSaveDO {
    /** 任务名称 */
    name: string;
    /** 目标数据板块ID */
    targetProjectId: number;
    /** 目标主题域id */
    targetDomainId: string;
    /** 目标表id */
    targetDomainTableId: number;
    /** 目标表-表名 */
    targetTableName: string;
    /** 应用说明--备注 */
    remark?: string;
  }

  interface FieldMapping {
    /** 数据库 */
    database: string;
    /** 字段类型 */
    fieldType: string;
  }

  interface getApiAppApiFindListParams {
    pageNum: number;
    pageSize: number;
    typeId: number;
    appid: number;
  }

  interface getApiAppApiFindListYpzParams {
    appid: number;
    page: Pageable;
  }

  interface getApiDataCheckUrlParams {
    /** 地址 */
    url: string;
  }

  interface getApiDataFindAllByApiIdParams {
    apiId: number;
  }

  interface getApiDataFindByParentIdAndVersionParams {
    /** 接口parentApiId */
    parentApiId: number;
    /** 接口版本号 */
    version: string;
  }

  interface getApiDataFindParams {
    /** 当前api接口id */
    id: string;
  }

  interface getApiDataFindVersionListParams {
    /** 当前api接口id */
    id: string;
  }

  interface getApiDataGetApiDomainListParams {
    /** 数据板块id */
    projectId: string;
  }

  interface getApiDataGetApiInOutParams {
    /** 表对应元数据的id */
    tableId: string;
  }

  interface getApiDataGetTableMsgParams {
    /** 数据板块id */
    projectId: string;
    /** 主题域id */
    domainId: string;
  }

  interface getAssetcatalogQueueDetailParams {
    /** 消息队列id */
    id: string;
  }

  interface getAssetcatalogQueueQueryListParams {
    /** 数据板块id */
    projectid: string;
    /** 主题域id */
    domainId: string;
  }

  interface getAssetcatalogTableDetailParams {
    /** 数据表id */
    id: string;
  }

  interface getAssetcatalogTableQueryListParams {
    /** 数据板块id */
    projectid: string;
    /** 主题域id */
    domainId: string;
  }

  interface getDataArchitectureDataAssetPermissionSavePermissionParams {
    /** 用户id */
    owner: string;
  }

  interface getDataArchitectureDataProjectDomainEntityDetailParams {
    /** 数据主题域实体历史记录ids */
    ids: string;
  }

  interface getDataArchitectureDataProjectDomainEntityFindOneParams {
    /** 数据主题域实体id */
    id: string;
  }

  interface getDataArchitectureDataProjectDomainEntityGetRelationParams {
    /** 数据主题域实体id */
    Tableid: string;
  }

  interface getDataArchitectureDataProjectDomainEntityQueryTreeParams {
    /** 数据板块id */
    projectid: string;
    /** 数据主题域实体id，默认为0 */
    id?: string;
  }

  interface getDataArchitectureDataProjectDomainEntityVersionListParams {
    /** 数据主题域实体id */
    id: string;
  }

  interface getDataArchitectureDataProjectDomainQueryListParams {
    /** 数据板块id */
    projectid?: string;
    /** 数据主题域名称 */
    name?: string;
  }

  interface getDataArchitectureDataProjectDomainQueryTreeParams {
    /** 数据板块id */
    projectid: string;
    /** 数据主题域id，默认为0 */
    id?: string;
  }

  interface getDataArchitectureDataProjectListBusinessParams {
    /** 数据板块id */
    id: number;
  }

  interface getDataMetaTaskSourceByCollectIdParams {
    /** 元数据采集结果id */
    collectId: string;
  }

  interface getDataMetaTaskTableColumnsParams {
    /** 元数据采集结果id */
    collectId: string;
  }

  interface getDataSourceGetDataSourceByTypeParams {
    /** 数据源类型 */
    sourceType: string;
  }

  interface getDataSourceGetProjectDataSourceParams {
    /** 数据源类型 */
    sourceType: string;
  }

  interface getDataSourceGetTableByIdParams {
    /** 数据源id */
    id: string;
  }

  interface getMonitorAppListParams {
    name?: string;
  }

  interface getMonitorEmailFindListParams {
    monitorUserDO: MonitorUserDO;
  }

  interface HeaderInVO {
    /** 请求头字段名称 */
    headerParaName?: string;
    /** 请求头字段值 */
    headerParaValue?: string;
    /** 请求头字段描述 */
    headerParaDesc?: string;
  }

  interface IdBase {
    /** 查询参数ID */
    id: number;
  }

  interface IncrTaskId {
    /** 增量任务节点配置id */
    incrTaskId: number;
  }

  interface IncrTaskScript {
    /** id,新增时为空 */
    incrTaskId?: number;
    /** 脚本 */
    rawScript?: string;
  }

  type JsonNode = true;

  interface JSONObject {
    empty?: boolean;
  }

  interface KafkaInfoSaveDto {
    /** 新增时 id 为空 */
    id?: number;
    name?: string;
    /** 多个server使用逗号分隔 */
    servers?: string;
  }

  interface Layerid {
    /** 数仓分层id */
    layerid: number;
  }

  interface LogQueryResp {
    /** 行数 */
    lineNum?: number;
    /** 日志内容 */
    message?: string;
  }

  interface MainDataQueryPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 数仓id */
    modelid?: number;
    /** 内容搜索，可搜索模型名称和模型表名 */
    content?: string;
  }

  interface MetaColumn {
    /** schema 名称 */
    schemaName?: string;
    /** 数据表名 */
    tableName?: string;
    /** 字段名 */
    name?: string;
    /** 字段数据类型 */
    dataType?: string;
    /** 字段长度 */
    dataLength?: string;
    /** 字段注释 */
    comments?: string;
    /** 字段默认值 */
    defaultVal?: string;
    /** 字符集 */
    charset?: string;
    /** 校对规则 */
    collation?: string;
    /** 字段约束 */
    constraint?: 'PRIMARY' | 'UNIQUE' | 'MULTIPLE' | 'PARTITION' | 'UNDEFINED' | 'NONE';
    /** 是否允许为空: true-允许, false-不允许 */
    nullable?: boolean;
    /** 平台自定字段类型取 Flink对应类型 */
    fieldType?: string;
    /** 中文简称 */
    zhCn?: string;
    /** 值域 */
    range?: string;
    /** 是否敏感: true-是, false-否 */
    sensitive?: boolean;
    /** 加密算法 */
    encryptionAlgorithm?: number;
  }

  interface MetaStatus {
    /** 注册元数据id */
    id: number;
    /** 状态：0-草稿，1-上线，2-下线 */
    status: number;
    /** 是否覆盖数据（默认为false） */
    cover?: boolean;
  }

  interface MissionEditDto {
    /** id */
    id: number;
    /** 任务状态 0:下线 1:上线 */
    status: number;
  }

  interface Modelid {
    /** 模型id */
    modelid: number;
  }

  interface MonitorApp {
    id?: number;
    /** 应用名 */
    name?: string;
    /** ip */
    ip?: string;
    /** 端口 */
    port?: string;
    /** 记录时间 */
    createTime?: string;
    /** 最后存活时间 */
    aliveTime?: string;
    /** 异常报警时间 */
    sendTime?: string;
    /** 状态(正常，异常) */
    status?: string;
  }

  interface MonitorAppDto {
    /** 应用名称 */
    name?: string;
    /** 应用节点列表 */
    list?: MonitorApp[];
    /** 节点总数 */
    total?: number;
    /** 节点正常数 */
    aliveNum?: number;
    order?: number;
  }

  interface MonitorUserDO {
    /** 当前页 */
    pageNum: number;
    /** 每页大小 */
    pageSize: number;
  }

  interface MonitorUserSaveDto {
    /** id,新增时不用传 */
    id?: number;
    /** 报警人 */
    name: string;
    /** 邮箱地址 */
    email: string;
  }

  interface OptionVoStringLong {
    label?: string;
    value?: number;
  }

  interface OptionVoStringString {
    label?: string;
    value?: string;
  }

  interface Pageable {
    page?: number;
    size?: number;
    sort?: string[];
  }

  interface PageableObject {
    offset?: number;
    sort?: SortObject;
    paged?: boolean;
    pageNumber?: number;
    pageSize?: number;
    unpaged?: boolean;
  }

  interface PageBase {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
  }

  interface PageDataDataApiListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataApiListVo[];
    total?: number;
  }

  interface PageDataDataMetaTablePageVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataMetaTablePageVo[];
    total?: number;
  }

  interface PageDataDataMetaTaskPageVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataMetaTaskPageVo[];
    total?: number;
  }

  interface PageDataDataMissionWarehouseDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataMissionWarehouseDto[];
    total?: number;
  }

  interface PageDataDataProjectDomainEntityHistoryListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectDomainEntityHistoryListVo[];
    total?: number;
  }

  interface PageDataDataProjectDomainEntityListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectDomainEntityListVo[];
    total?: number;
  }

  interface PageDataDataProjectDomainEntityTableListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectDomainEntityTableListVo[];
    total?: number;
  }

  interface PageDataDataProjectDomainTableListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectDomainTableListVo[];
    total?: number;
  }

  interface PageDataDataProjectDomainVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectDomainVo[];
    total?: number;
  }

  interface PageDataDataProjectVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataProjectVo[];
    total?: number;
  }

  interface PageDataDataQueueListVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataQueueListVo[];
    total?: number;
  }

  interface PageDataDataSourcePageVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataSourcePageVo[];
    total?: number;
  }

  interface PageDataDataWarehouseModelSimpleDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataWarehouseModelSimpleDto[];
    total?: number;
  }

  interface PageDataDataWorkflowDefineRow {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataWorkflowDefineRow[];
    total?: number;
  }

  interface PageDataDataWorkflowInstance {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DataWorkflowInstance[];
    total?: number;
  }

  interface PageDataDsTaskInstance {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: DsTaskInstance[];
    total?: number;
  }

  interface PageDataElementListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DataElementListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDataMapStringObject {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: Record<string, any>[];
    total?: number;
  }

  interface PageDataPubUserDto {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: PubUserDto[];
    total?: number;
  }

  interface PageDataQualityTaskListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DataQualityTaskListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDataSecurityKeyListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DataSecurityKeyListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDataStandardDictListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DataStandardDictListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDataTableVersionVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: TableVersionVo[];
    total?: number;
  }

  interface PageDataTreeVo {
    currentPage?: number;
    pageSize?: number;
    doSearchTotal?: boolean;
    records?: TreeVo[];
    total?: number;
  }

  interface PageDataWorkflowInstance {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DataWorkflowInstance[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDesensitizationTemplateListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DesensitizationTemplateListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDetectionResultVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DetectionResultVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageDynamicMaskRuleListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: DynamicMaskRuleListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageEncryptionAlgorithmListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: EncryptionAlgorithmListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageQualityTaskLogListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: QualityTaskLogListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageRuleTemplateListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: RuleTemplateListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageScanTaskListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: ScanTaskListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageSecurityClassListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: SecurityClassListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageSensitiveLabelListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: SensitiveLabelListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface PageStandardFileListVo {
    totalElements?: number;
    totalPages?: number;
    size?: number;
    content?: StandardFileListVo[];
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
  }

  interface postApiDataRemoveParams {
    /** 当前api接口id */
    id: string;
  }

  interface postApiUserDeleteUserParams {
    userId: number;
  }

  interface postAssetcatalogQueueDeletedParams {
    /** 消息队列id */
    id: string;
  }

  interface postDataArchitectureDataProjectDeletedParams {
    /** 数据板块id */
    id: string;
  }

  interface postDataArchitectureDataProjectDomainDeletedParams {
    /** 数据主题域id */
    id: string;
  }

  interface postDataArchitectureDataProjectDomainEntityCancelTableParams {
    /** 数据实体关联表id */
    id: string;
  }

  interface postDataArchitectureDataProjectDomainEntityChangeParams {
    /** 数据主题域实体id */
    id: string;
  }

  interface postDataArchitectureDataProjectDomainEntityDeletedParams {
    /** 数据主题域实体id */
    id: string;
  }

  interface postDataIntegrationMethodExcelImportExcelParams {
    excelid: number;
  }

  interface postDataMetaTaskShutdownParams {
    /** 任务总线id */
    id: string;
  }

  interface postDataMetaTaskStartupParams {
    /** 任务总线id */
    id: string;
  }

  interface postDataSecurityDesenTemplateUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDataSecurityDynamicMaskRuleUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDataSecurityEncryptionAlgorithmUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDataSecurityKeyGenKeyParams {
    /** 加密方式id */
    id: string;
    /** 密钥位数 */
    keyBits?: string;
    /** 私钥密码 */
    keyPassword?: string;
  }

  interface postDataSecurityKeyUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDataStandardDictionaryDictImportParams {
    directoryId: number;
    directoryNodeType: number;
  }

  interface postDataStandardDictionaryDictUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDataStandardDictionaryDirectoryTreeParams {
    name?: string;
  }

  interface postDataStandardElementDirectoryTreeParams {
    name?: string;
  }

  interface postDataStandardElementElementDefFieldMappingParams {
    /** 数据类型 */
    dataType: string;
  }

  interface postDataStandardElementElementImportParams {
    directoryId: number;
    directoryNodeType: number;
  }

  interface postDataStandardElementElementUpdStatusParams {
    id: number;
    status: number;
  }

  interface postDictDataCheckParams {
    params: string;
  }

  interface postDictDataDelParams {
    params: string;
  }

  interface postFileUploadPhotosParams {
    file: string[];
  }

  interface postInfoDbAddParams {
    sourceId: number;
  }

  interface postInfoDbDelParams {
    /** Database表 id */
    id: number;
  }

  interface postInfoDbGetParams {
    /** Database表 id */
    id: number;
  }

  interface postInfoEsDelParams {
    /** EsInfo表 id */
    id: number;
  }

  interface postInfoEsGetParams {
    /** EsInfo表 id */
    id: number;
  }

  interface postInfoKafkaDelParams {
    /** KafkaInfo表 id */
    id: number;
  }

  interface postInfoKafkaGetParams {
    /** KafkaInfo表 id */
    id: number;
  }

  interface postInfoRedisDelParams {
    /** RedisInfo表 id */
    id: number;
  }

  interface postInfoRedisGetParams {
    /** RedisInfo表 id */
    id: number;
  }

  interface postInfoServerGetParams {
    /** ServerInfo表 ip */
    ip: string;
  }

  interface postMonitorEmailDeleteByIdParams {
    id: number;
  }

  interface postSortDltParams {
    /** 分类ID */
    id: number;
  }

  interface postSortFindParams {
    /** 分类ID */
    id: number;
  }

  interface postSortListParams {
    /** 父分类ID */
    pid: number;
  }

  interface postSortSimplelistParams {
    /** 父分类ID */
    pid: number;
  }

  interface ProjectVo {
    /** id */
    id?: number;
    /** 数据板块中文名称 */
    zh_name?: string;
    /** 数据板块英文名称 */
    en_name?: string;
    /** 数据源集合 */
    sourceList?: DataSource[];
  }

  interface PubUserDto {
    /** 用户id */
    id?: string;
    /** 用户名称 */
    name?: string;
  }

  interface QualityDetail {
    /** 规则类型 */
    ruleType?: string;
    /** 得分(默认为0) */
    scoreDefZero?: string;
    /** 得分 */
    score?: string;
    /** 检查详情 */
    detail?: string;
  }

  interface QualityTaskLogListVo {
    /** 报告id */
    reportId?: number;
    /** 任务实例id */
    instanceId?: string;
    /** 任务名称 */
    taskName?: string;
    /** 执行结果 */
    result?: number;
    /** 任务实例耗时时间 */
    instanceDurTime?: string;
    /** 执行时间 */
    executeTime?: string;
  }

  interface QualityTaskLogVo {
    /** 任务实例id */
    instanceId?: string;
    /** 任务实例开始时间 */
    instanceStartTime?: string;
    /** 任务实例结束时间 */
    instanceEndTime?: string;
    /** 任务实例耗时时间 */
    instanceDurTime?: string;
    /** 任务实例规则检测数 */
    instanceRuleNum?: number;
    /** 任务实例规则检测成功数 */
    instanceRuleSuccessNum?: number;
    /** 任务实例规则检测失败数 */
    instanceRuleFailNum?: number;
    taskLog?: string;
  }

  interface QueueColumn {
    /** 名称 */
    name?: string;
    /** 字段名 */
    colname?: string;
    /** 数据库类型(字段类型) */
    coltype?: string;
    /** 类型 */
    type?: string;
  }

  interface RedisInfoSaveDto {
    /** 新增时 id 为空 */
    id?: number;
    name?: string;
    host?: string;
    port?: number;
    password?: string;
    usedMemoryRssHuman?: string;
    totalSystemMemoryHuman?: string;
    connectedClients?: number;
    keysNum?: number;
  }

  interface ResponseData {
    code?: number;
    msg?: string;
    hint?: string;
    data?: Record<string, any>;
    success?: boolean;
  }

  interface ResponseDataAdhocPageDataMapStringObject {
    code?: number;
    msg?: string;
    hint?: string;
    data?: AdhocPageDataMapStringObject;
    success?: boolean;
  }

  interface ResponseDataAdhocQueryResultPage {
    code?: number;
    msg?: string;
    hint?: string;
    data?: AdhocQueryResultPage;
    success?: boolean;
  }

  interface ResponseDataBoolean {
    code?: number;
    msg?: string;
    hint?: string;
    data?: boolean;
    success?: boolean;
  }

  interface ResponseDataCollectTableSourceVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: CollectTableSourceVo;
    success?: boolean;
  }

  interface ResponseDataDataElementDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataElementDto;
    success?: boolean;
  }

  interface ResponseDataDataMetaTableCollectVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataMetaTableCollectVo;
    success?: boolean;
  }

  interface ResponseDataDataMetaTableVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataMetaTableVo;
    success?: boolean;
  }

  interface ResponseDataDataMetaTaskVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataMetaTaskVo;
    success?: boolean;
  }

  interface ResponseDataDataProjectDomainEntityHistoryVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomainEntityHistoryVo;
    success?: boolean;
  }

  interface ResponseDataDataProjectDomainEntityVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomainEntityVo;
    success?: boolean;
  }

  interface ResponseDataDataProjectDomainTableDetailVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomainTableDetailVo;
    success?: boolean;
  }

  interface ResponseDataDataQualityTaskDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataQualityTaskDto;
    success?: boolean;
  }

  interface ResponseDataDataSecurityKeyDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataSecurityKeyDto;
    success?: boolean;
  }

  interface ResponseDataDataSecurityScanTaskDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataSecurityScanTaskDto;
    success?: boolean;
  }

  interface ResponseDataDataWarehouseModel {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWarehouseModel;
    success?: boolean;
  }

  interface ResponseDataDataWorkflowDefineSave {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWorkflowDefineSave;
    success?: boolean;
  }

  interface ResponseDataDataWorkflowIncrTaskDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWorkflowIncrTaskDto;
    success?: boolean;
  }

  interface ResponseDataDataWorkflowPlanDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWorkflowPlanDto;
    success?: boolean;
  }

  interface ResponseDataDesensitizationTemplateDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DesensitizationTemplateDto;
    success?: boolean;
  }

  interface ResponseDataDictionaryDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DictionaryDto;
    success?: boolean;
  }

  interface ResponseDataDynamicMaskRuleDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DynamicMaskRuleDto;
    success?: boolean;
  }

  interface ResponseDataEncryptInfo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptInfo;
    success?: boolean;
  }

  interface ResponseDataEncryptionAlgorithmDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptionAlgorithmDto;
    success?: boolean;
  }

  interface ResponseDataEncryptTaskDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptTaskDto;
    success?: boolean;
  }

  interface ResponseDataEncryptTaskScript {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptTaskScript;
    success?: boolean;
  }

  interface ResponseDataIncrTaskScript {
    code?: number;
    msg?: string;
    hint?: string;
    data?: IncrTaskScript;
    success?: boolean;
  }

  interface ResponseDataListDataAssetPermissionHistory {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataAssetPermissionHistory[];
    success?: boolean;
  }

  interface ResponseDataListDataAssetPermissionListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataAssetPermissionListVo[];
    success?: boolean;
  }

  interface ResponseDataListDataElementHistoryVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataElementHistoryVo[];
    success?: boolean;
  }

  interface ResponseDataListDataMetaTablePublish {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataMetaTablePublish[];
    success?: boolean;
  }

  interface ResponseDataListDataProjectDomain {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomain[];
    success?: boolean;
  }

  interface ResponseDataListDataProjectDomainEntityRelationTreeVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomainEntityRelationTreeVo[];
    success?: boolean;
  }

  interface ResponseDataListDataProjectDomainTableSimpleListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataProjectDomainTableSimpleListVo[];
    success?: boolean;
  }

  interface ResponseDataListDataQualityTaskResultReport {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataQualityTaskResultReport[];
    success?: boolean;
  }

  interface ResponseDataListDataQueueSimpleListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataQueueSimpleListVo[];
    success?: boolean;
  }

  interface ResponseDataListDataSource {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataSource[];
    success?: boolean;
  }

  interface ResponseDataListDataSourceSimpleDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataSourceSimpleDto[];
    success?: boolean;
  }

  interface ResponseDataListDataSourceType {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataSourceType[];
    success?: boolean;
  }

  interface ResponseDataListDataWarehouseLayerMap {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWarehouseLayerMap[];
    success?: boolean;
  }

  interface ResponseDataListDataWarehouseRule {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DataWarehouseRule[];
    success?: boolean;
  }

  interface ResponseDataListDictData {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DictData[];
    success?: boolean;
  }

  interface ResponseDataListDictDataColumnType {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DictDataColumnType[];
    success?: boolean;
  }

  interface ResponseDataListDirectoryDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DirectoryDto[];
    success?: boolean;
  }

  interface ResponseDataListDiscStatusStatVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DiscStatusStatVo[];
    success?: boolean;
  }

  interface ResponseDataListDsDataSourceSimple {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DsDataSourceSimple[];
    success?: boolean;
  }

  interface ResponseDataListDsWorkerGroup {
    code?: number;
    msg?: string;
    hint?: string;
    data?: DsWorkerGroup[];
    success?: boolean;
  }

  interface ResponseDataListEncryptionAlgorithmNewListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptionAlgorithmNewListVo[];
    success?: boolean;
  }

  interface ResponseDataListEncryptionMethodVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: EncryptionMethodVo[];
    success?: boolean;
  }

  interface ResponseDataListFieldMapping {
    code?: number;
    msg?: string;
    hint?: string;
    data?: FieldMapping[];
    success?: boolean;
  }

  interface ResponseDataListOptionVoStringLong {
    code?: number;
    msg?: string;
    hint?: string;
    data?: OptionVoStringLong[];
    success?: boolean;
  }

  interface ResponseDataListOptionVoStringString {
    code?: number;
    msg?: string;
    hint?: string;
    data?: OptionVoStringString[];
    success?: boolean;
  }

  interface ResponseDataListProjectVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: ProjectVo[];
    success?: boolean;
  }

  interface ResponseDataListPubUserDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PubUserDto[];
    success?: boolean;
  }

  interface ResponseDataListSecurityClassListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: SecurityClassListVo[];
    success?: boolean;
  }

  interface ResponseDataListStandardFileStatVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: StandardFileStatVo[];
    success?: boolean;
  }

  interface ResponseDataListStatusStatVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: StatusStatVo[];
    success?: boolean;
  }

  interface ResponseDataListString {
    code?: number;
    msg?: string;
    hint?: string;
    data?: string[];
    success?: boolean;
  }

  interface ResponseDataListTablePublishVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: TablePublishVo[];
    success?: boolean;
  }

  interface ResponseDataLogQueryResp {
    code?: number;
    msg?: string;
    hint?: string;
    data?: LogQueryResp;
    success?: boolean;
  }

  interface ResponseDataLong {
    code?: number;
    msg?: string;
    hint?: string;
    data?: number;
    success?: boolean;
  }

  interface ResponseDataMetaColumn {
    code?: number;
    msg?: string;
    hint?: string;
    data?: MetaColumn;
    success?: boolean;
  }

  interface ResponseDataObject {
    code?: number;
    msg?: string;
    hint?: string;
    data?: Record<string, any>;
    success?: boolean;
  }

  interface ResponseDataPageDataDataApiListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataApiListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataMetaTablePageVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataMetaTablePageVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataMetaTaskPageVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataMetaTaskPageVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataMissionWarehouseDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataMissionWarehouseDto;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectDomainEntityHistoryListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectDomainEntityHistoryListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectDomainEntityListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectDomainEntityListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectDomainEntityTableListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectDomainEntityTableListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectDomainTableListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectDomainTableListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectDomainVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectDomainVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataProjectVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataProjectVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataQueueListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataQueueListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataSourcePageVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataSourcePageVo;
    success?: boolean;
  }

  interface ResponseDataPageDataDataWarehouseModelSimpleDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataWarehouseModelSimpleDto;
    success?: boolean;
  }

  interface ResponseDataPageDataDataWorkflowDefineRow {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataWorkflowDefineRow;
    success?: boolean;
  }

  interface ResponseDataPageDataDataWorkflowInstance {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDataWorkflowInstance;
    success?: boolean;
  }

  interface ResponseDataPageDataDsTaskInstance {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataDsTaskInstance;
    success?: boolean;
  }

  interface ResponseDataPageDataElementListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataElementListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataMapStringObject {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataMapStringObject;
    success?: boolean;
  }

  interface ResponseDataPageDataPubUserDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataPubUserDto;
    success?: boolean;
  }

  interface ResponseDataPageDataQualityTaskListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataQualityTaskListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataSecurityKeyListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataSecurityKeyListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataStandardDictListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataStandardDictListVo;
    success?: boolean;
  }

  interface ResponseDataPageDataTableVersionVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataTableVersionVo;
    success?: boolean;
  }

  interface ResponseDataPageDataTreeVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataTreeVo;
    success?: boolean;
  }

  interface ResponseDataPageDataWorkflowInstance {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDataWorkflowInstance;
    success?: boolean;
  }

  interface ResponseDataPageDesensitizationTemplateListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDesensitizationTemplateListVo;
    success?: boolean;
  }

  interface ResponseDataPageDetectionResultVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDetectionResultVo;
    success?: boolean;
  }

  interface ResponseDataPageDynamicMaskRuleListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageDynamicMaskRuleListVo;
    success?: boolean;
  }

  interface ResponseDataPageEncryptionAlgorithmListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageEncryptionAlgorithmListVo;
    success?: boolean;
  }

  interface ResponseDataPageQualityTaskLogListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageQualityTaskLogListVo;
    success?: boolean;
  }

  interface ResponseDataPageRuleTemplateListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageRuleTemplateListVo;
    success?: boolean;
  }

  interface ResponseDataPageScanTaskListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageScanTaskListVo;
    success?: boolean;
  }

  interface ResponseDataPageSecurityClassListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageSecurityClassListVo;
    success?: boolean;
  }

  interface ResponseDataPageSensitiveLabelListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageSensitiveLabelListVo;
    success?: boolean;
  }

  interface ResponseDataPageStandardFileListVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: PageStandardFileListVo;
    success?: boolean;
  }

  interface ResponseDataQualityTaskLogVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: QualityTaskLogVo;
    success?: boolean;
  }

  interface ResponseDataScanTaskLogVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: ScanTaskLogVo;
    success?: boolean;
  }

  interface ResponseDataSensitiveLabelDto {
    code?: number;
    msg?: string;
    hint?: string;
    data?: SensitiveLabelDto;
    success?: boolean;
  }

  interface ResponseDataString {
    code?: number;
    msg?: string;
    hint?: string;
    data?: string;
    success?: boolean;
  }

  interface ResponseDataTableDataVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: TableDataVo;
    success?: boolean;
  }

  interface ResponseDataTaskResultReportVo {
    code?: number;
    msg?: string;
    hint?: string;
    data?: TaskResultReportVo;
    success?: boolean;
  }

  interface RuleTemplateListVo {
    /** id */
    id?: number;
    /** 规则名称 */
    ruleName?: string;
    /** 规则类型 */
    ruleType?: string;
    /** 规则说明 */
    ruleDesc?: string;
    /** 评分规则 */
    gradeRule?: string;
  }

  interface ScanTaskListVo {
    /** id */
    id?: number;
    /** 任务名称 */
    taskName?: string;
    /** 任务描述 */
    taskDesc?: string;
    /** 敏感标签 */
    sensitiveLabel?: string;
    /** 任务状态 */
    status?: string;
    /** 最近执行 */
    lastExecuteTime?: string;
    /** 实例id */
    instanceId?: string;
  }

  interface ScanTaskLogVo {
    /** 任务实例id */
    instanceId?: string;
    /** 任务实例开始时间 */
    instanceStartTime?: string;
    /** 任务实例结束时间 */
    instanceEndTime?: string;
    /** 任务实例耗时时间 */
    instanceDurTime?: string;
    taskLog?: string;
  }

  interface SearchInfoSaveDto {
    id?: number;
    name?: string;
    /** 多个uri使用逗号分隔 */
    uris?: string;
    username?: string;
    password?: string;
  }

  interface SecurityClassDto {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** id */
    id?: number;
    /** 密级 */
    level: string;
    /** 名称 */
    name: string;
    /** 密级描述 */
    description: string;
  }

  interface SecurityClassListVo {
    /** id */
    id?: number;
    /** 密级 */
    level?: string;
    /** 名称 */
    name?: string;
    /** 密级描述 */
    description?: string;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface SecurityClassQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
  }

  interface SensitiveLabelDto {
    /** id */
    id?: number;
    /** 标签名称 */
    labelName: string;
    /** 安全密级id */
    securityLevelId: number;
    /** 关联数据元ID */
    relatedDataElementId?: number;
    /** 名称识别规则 */
    nameIdentificationRule?: string;
    /** 内容识别规则 */
    contentIdentificationRule?: string;
  }

  interface SensitiveLabelListVo {
    /** id */
    id?: number;
    /** 标签名称 */
    labelName?: string;
    /** 安全等级 */
    securityLevel?: string;
    /** 识别方式 */
    identificationMethod?: string;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface SensitiveLabelQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
  }

  interface SetAdminDto {
    password: string;
    userId: number;
    type: number;
  }

  interface shujufenleicanshubianji {
    待编辑的分类id: number;
    分类名称: string;
    排序: number;
    备注说明?: string;
  }

  interface shujufenleicanshuxinzeng {
    分类名称: string;
    父分类id: number;
    排序: number;
    备注说明?: string;
  }

  interface SortObject {
    empty?: boolean;
    sorted?: boolean;
    unsorted?: boolean;
  }

  interface StandardFileDownLoadQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 标准分类 */
    category?: string;
    /** idList */
    idList?: number[];
  }

  interface StandardFileDto {
    /** 中文名称 */
    chineseName: string;
    /** 标准分类 */
    category: string;
    /** 版本 */
    version: string;
    /** 文件URI */
    fileUri: string;
    /** 说明 */
    description?: string;
  }

  interface StandardFileListVo {
    /** id */
    id?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 标准分类 */
    category?: string;
    /** 版本 */
    version?: string;
    /** 文件URI */
    fileUri?: string;
    /** 创建人 */
    creator?: string;
    /** 创建时间 */
    createdAt?: string;
  }

  interface StandardFileQuery {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 标准分类 */
    category?: string;
  }

  interface StandardFileStatVo {
    /** code */
    code?: string;
    /** 名称 */
    name?: string;
    /** 数量 */
    count?: number;
  }

  interface StatusStatVo {
    /** code */
    code?: number;
    /** 名称 */
    name?: string;
    /** 数量 */
    count?: number;
  }

  interface SystemLog {
    id?: number;
    /** 应用名称 */
    name?: string;
    /** 应用部署地址 */
    serverip?: string;
    /** 应用部署端口 */
    serverport?: string;
    /** java虚拟机正常运行时间 毫秒 */
    uptime?: number;
    /** 总线程数 */
    threadCount?: number;
    /** 堆内存初始化大小 */
    heapInit?: number;
    /** 堆内存已使用大小 */
    heapUsed?: number;
    /** 堆内存已提交大小 */
    heapCommitted?: number;
    /** 堆内存最大值 */
    heapMax?: number;
    /** 非堆内存初始化大小 */
    nonheapInit?: number;
    /** 非堆内存已使用大小 */
    nonheapUsed?: number;
    /** 非堆内存已提交大小 */
    nonheapCommitted?: number;
    /** 非堆内存最大值 */
    nonheapMax?: number;
    /** 过去一分钟系统负载平均值 越大越重 */
    loadAverage?: number;
    /** 应用所在盘大小 */
    totalSpace?: number;
    /** 应用所在盘剩余可使用大小 */
    usableSpace?: number;
    /** 当前系统时间 yyyy-MM-dd HH:mm:ss */
    systemTime?: string;
    /** 当前系统时间 */
    createTime?: string;
    /** cpu数量 */
    cpus?: number;
    /** 总磁盘大小 */
    diskTotal?: string;
    /** 总磁盘使用量 */
    diskUsed?: string;
    /** 总内存大小 */
    ramMax?: string;
    /** 当前使用内存 */
    ramCurrent?: string;
    /** CPU% */
    cpuPercent?: number;
    nameAndIpAndPort?: string;
  }

  interface SystemLogDto {
    id?: number;
    /** 应用名 */
    name?: string;
    /** ip */
    serverip?: string;
    /** 端口 */
    serverport?: string;
    /** java虚拟机正常运行时间:毫秒 */
    uptime?: number;
    /** 总线程数 */
    threadCount?: number;
    /** 堆内存初始化大小 */
    heapInit?: string;
    /** 堆内存已使用大小 */
    heapUsed?: string;
    /** 堆内存已提交大小 */
    heapCommitted?: string;
    /** 堆内存最大值 */
    heapMax?: string;
    /** 非堆内存初始化大小 */
    nonheapInit?: string;
    /** 非堆内存已使用大小 */
    nonheapUsed?: string;
    /** 非堆内存已提交大小 */
    nonheapCommitted?: string;
    /** 非堆内存最大值 */
    nonheapMax?: string;
    /** 系统负载平均值 */
    loadAverage?: number;
    /** 应用所在盘大小 */
    totalSpace?: string;
    /** 应用所在盘剩余可使用大小 */
    usableSpace?: string;
    /** 当前系统时间,格式：yyyy-MM-dd HH:mm:ss */
    systemTime?: string;
    nameAndIpAndPort?: string;
  }

  interface SystemLogQueryInfo {
    /** 应用名 */
    name: string;
    /** ip */
    serverip: string;
    /** 端口 */
    serverport: string;
    /** 查询开始时间，yyyy-MM-dd HH:mm */
    startTime?: string;
    /** 查询结束时间，yyyy-MM-dd HH:mm */
    endTime?: string;
    /** 页码 */
    page?: number;
    /** 每页条数 */
    rows?: number;
  }

  interface TableDataQueryPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 资产表id */
    domainTableId?: number;
    /** 内容搜索 */
    content?: string;
  }

  interface TableDataVo {
    columnList?: string[];
    dataList?: JSONObject[];
  }

  interface TablePublishVo {
    /** id */
    id?: number;
    /** 中文名称 */
    chineseName?: string;
    /** 表名 */
    tableName?: string;
    tableColumns?: JsonNode;
  }

  interface TableVersionPage {
    /** 当前页 */
    currentPage?: number;
    /** 每页大小 */
    pageSize?: number;
    /** 元数据初始ID */
    initId: number;
  }

  interface TableVersionVo {
    /** 元数据ID */
    id?: number;
    /** 初始ID */
    initId?: number;
    /** 元数据版本号 */
    version?: string;
    /** 创建者ID */
    createBy?: string;
    /** 创建者 */
    creater?: string;
    /** 更新时间 */
    updatedAt?: string;
    /** 删除状态，0：正常；1：删除 */
    deleted?: number;
  }

  interface TaskResultReportVo {
    id?: number;
    /** 表所属主题域名称 */
    domainName?: string;
    /** 表名 */
    tableName?: string;
    /** 检测时间 */
    checkTime?: string;
    /** 综合得分 */
    totalScore?: string;
    /** 检查详情列表 */
    detailList?: QualityDetail[];
  }

  interface TreeVo {
    /** id */
    id?: number;
    /** 父级(id) */
    pid?: number;
    /** 名称 */
    name?: string;
    /** 排序序号 */
    seq?: number;
    /** 子节点 */
    children?: TreeVo[];
  }

  interface UpdateUserDO {
    userId?: number;
    status?: number;
  }

  interface yingyongpeizhijiekousqlceshijieguo {
    /** 实际执行的sql */
    realSql?: string;
    /** 检测标识（检测成功时返回），保存时需要带上此值 */
    sign?: string;
    /** 执行结果，成功或者失败信息 */
    result?: string;
  }
}
