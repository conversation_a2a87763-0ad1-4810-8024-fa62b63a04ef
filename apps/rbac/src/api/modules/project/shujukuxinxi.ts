import { basic as request } from "@/api/request";

/**
 * @description 新增数据库信息（根据数据源id）
 * @url /info/db/add
 * @method POST
 * <AUTHOR>
 */
export function postInfoDbAdd<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoDbAddParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/info/db/add",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 逻辑删除数据库信息 (by id)
 * @url /info/db/del
 * @method POST
 * <AUTHOR>
 */
export function postInfoDbDel<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoDbDelParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/info/db/del",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 获取数据库信息
 * @url /info/db/get
 * @method POST
 * <AUTHOR>
 */
export function postInfoDbGet<R = Record<string, any>, T = Record<string, any>>(
  params: API.postInfoDbGetParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/info/db/get",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 获取全部数据库信息
 * @url /info/db/getAll
 * @method POST
 * <AUTHOR>
 */
export function postInfoDbGetAll<
  R = Record<string, any>,
  T = Record<string, any>
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/info/db/getAll",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 查询数据源列表
 * @url /info/db/sources
 * @method GET
 * <AUTHOR>
 */
export function getInfoDbSources<
  R = Record<string, any>,
  T = Record<string, any>
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>("/info/db/sources", {
    ...(options || {}),
  });
}

/**
 * @description [添加/更新]数据库信息
 * @url /info/db/upsert
 * @method POST
 * <AUTHOR>
 */
export function postInfoDbUpsert<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.DatabaseSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/info/db/upsert", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
