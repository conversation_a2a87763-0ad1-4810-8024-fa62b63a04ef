import { basic as request } from "@/api/request";

/**
 * @description 根据apiid列表获取地址
 * @url /apiData/node/urls/byapiids
 * @method POST
 * <AUTHOR>
 */
export function postApiDataNodeUrlsByapiids<
  R = Record<string, any>,
  T = Record<string, any>
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/apiData/node/urls/byapiids", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
