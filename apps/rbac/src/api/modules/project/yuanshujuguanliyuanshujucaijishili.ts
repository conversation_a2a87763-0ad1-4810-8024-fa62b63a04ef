import { basic as request } from "@/api/request";

/**
 * @description 根据ds任务实例id获取运行日志信息 元数据采集任务实例
 * @url /data/meta/instance/findTaskRunLog
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaInstanceFindTaskRunLog<
  R = API.ResponseData,
  T = API.ResponseData
>(
  body: API.DataMetaTableLogDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/instance/findTaskRunLog", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询工作流实例信息 元数据采集任务实例
 * @url /data/meta/instance/pageWorkflowInstance
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaInstancePageWorkflowInstance<
  R = API.ResponseDataPageDataWorkflowInstance,
  T = API.ResponseDataPageDataWorkflowInstance
>(
  body: API.DataMetaWorkflowPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/instance/pageWorkflowInstance", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 根据工作流实例id获取采集的元数据信息 元数据采集任务实例
 * @url /data/meta/instance/result
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaInstanceResult<
  R = API.ResponseDataDataMetaTableCollectVo,
  T = API.ResponseDataDataMetaTableCollectVo
>(
  body: API.DataMetaTableCollectPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/instance/result", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
