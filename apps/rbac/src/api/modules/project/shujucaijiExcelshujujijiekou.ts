import { basic as request } from '@/api/request';

/**
 * @description 添加excel数据集
 * @url /dataIntegrationMethodExcel/add
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelAdd<R = Record<string, any>, T = Record<string, any>>(
  body: API.ExcelSetSaveDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/add', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据查询创建
 * @url /dataIntegrationMethodExcel/createPreview
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelCreatePreview<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.DataPreviewDO, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/createPreview', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 通过excel数据集id,删除对应数据
 * @url /dataIntegrationMethodExcel/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelDelete<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/delete', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 通过excel数据集id,查询对应数据
 * @url /dataIntegrationMethodExcel/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelDetail<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 编辑excel数据集
 * @url /dataIntegrationMethodExcel/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelEdit<
  R = Record<string, any>,
  T = Record<string, any>,
>(body: API.ExcelSetEditDO, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/edit', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 单独导入excel
 * @url /dataIntegrationMethodExcel/importExcel
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelImportExcel<
  R = Record<string, any>,
  T = Record<string, any>,
>(
  params: API.postDataIntegrationMethodExcelImportExcelParams,
  body: Record<string, unknown>,
  excel?: File,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const formData = new FormData();

  if (excel) {
    formData.append('excel', excel);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>('/dataIntegrationMethodExcel/importExcel', formData, {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description Excel采集分页查询 Excel采集分页查询
 * @url /dataIntegrationMethodExcel/page
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelPage<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject,
>(body: API.DataIntegrationMethodSqlPageDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 下载模板
 * @url /dataIntegrationMethodExcel/template
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodExcelTemplate<R = any, T = any>(
  body: API.IdBase,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataIntegrationMethodExcel/template', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
