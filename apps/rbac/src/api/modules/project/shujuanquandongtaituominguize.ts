import { basic as request } from "@/api/request";

/**
 * @description 批量删除动态脱敏规则 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRuleBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSecurity/dynamicMaskRule/batchDel", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 删除动态脱敏规则 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRuleDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSecurity/dynamicMaskRule/del", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 动态脱敏规则详情 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRuleDetail<
  R = API.ResponseDataDynamicMaskRuleDto,
  T = API.ResponseDataDynamicMaskRuleDto
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSecurity/dynamicMaskRule/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 动态脱敏规则列表 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRulePageList<
  R = API.ResponseDataPageDynamicMaskRuleListVo,
  T = API.ResponseDataPageDynamicMaskRuleListVo
>(
  body: API.DynamicMaskRuleQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSecurity/dynamicMaskRule/pageList", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改动态脱敏规则 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRuleUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.DynamicMaskRuleDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSecurity/dynamicMaskRule/upd", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改动态脱敏规则状态 动态脱敏规则
 * @url /dataSecurity/dynamicMaskRule/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityDynamicMaskRuleUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  params: API.postDataSecurityDynamicMaskRuleUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataSecurity/dynamicMaskRule/updStatus",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
