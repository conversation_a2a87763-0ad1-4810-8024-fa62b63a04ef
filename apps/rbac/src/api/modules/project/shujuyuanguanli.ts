import { basic as request } from "@/api/request";

/**
 * @description 数据源新增 数据源新增
 * @url /dataSource/add
 * @method POST
 * <AUTHOR>
 */
export function postDataSourceAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataSourceAddDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSource/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据源测试连接 数据源测试连接
 * @url /dataSource/connect
 * @method POST
 * <AUTHOR>
 */
export function postDataSourceConnect<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataSourceTestDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSource/connect", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据源删除 数据源删除
 * @url /dataSource/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataSourceDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSource/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 查询数据源详情
 * @url /dataSource/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSourceDetail<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSource/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据源编辑 数据源编辑
 * @url /dataSource/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataSourceEdit<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataSourceEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSource/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 根据数据源类型获取数据源下拉列表 数据源
 * @url /dataSource/getDataSourceByType
 * @method GET
 * <AUTHOR>
 */
export function getDataSourceGetDataSourceByType<
  R = API.ResponseDataListDataSource,
  T = API.ResponseDataListDataSource
>(
  params: API.getDataSourceGetDataSourceByTypeParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/dataSource/getDataSourceByType", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 查询数据板块下的数据源下拉列表（根据当前用户信息过滤板块） 数据源
 * @url /dataSource/getProjectDataSource
 * @method GET
 * <AUTHOR>
 */
export function getDataSourceGetProjectDataSource<
  R = API.ResponseDataListProjectVo,
  T = API.ResponseDataListProjectVo
>(
  params: API.getDataSourceGetProjectDataSourceParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/dataSource/getProjectDataSource", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据数据源id获取表结构列表 数据源表
 * @url /dataSource/getTableById
 * @method GET
 * <AUTHOR>
 */
export function getDataSourceGetTableById<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.getDataSourceGetTableByIdParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/dataSource/getTableById", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 数据源分页查询 数据源分页查询
 * @url /dataSource/page
 * @method POST
 * <AUTHOR>
 */
export function postDataSourcePage<
  R = API.ResponseDataPageDataDataSourcePageVo,
  T = API.ResponseDataPageDataDataSourcePageVo
>(
  body: API.DataSourcePageDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSource/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 获取数据源类型下拉框 数据源类型
 * @url /dataSource/type
 * @method GET
 * <AUTHOR>
 */
export function getDataSourceType<
  R = API.ResponseDataListDataSourceType,
  T = API.ResponseDataListDataSourceType
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>("/dataSource/type", {
    ...(options || {}),
  });
}
