import { basic as request } from "@/api/request";

/**
 * @description 数据采集-贴源采集新增 数据采集-贴源采集新增
 * @url /dataIntegrationMethodTable/add
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodTableAddDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodTable/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-贴源采集删除 数据采集-贴源采集删除
 * @url /dataIntegrationMethodTable/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodTable/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-贴源采集详情 数据采集-贴源采集详情
 * @url /dataIntegrationMethodTable/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableDetail<
  R = Record<string, any>,
  T = Record<string, any>
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodTable/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-贴源采集编辑 数据采集-贴源采集编辑
 * @url /dataIntegrationMethodTable/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableEdit<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodTableEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodTable/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 查询字段映射 查询字段映射
 * @url /dataIntegrationMethodTable/findMapping
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableFindMapping<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodTableMappingDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodTable/findMapping", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 查询目标表字段映射 查询目标表字段映射
 * @url /dataIntegrationMethodTable/findTargetMapping
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableFindTargetMapping<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataIntegrationMethodTable/findTargetMapping",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据采集-获取增量类型列表 数据采集-获取增量类型列表
 * @url /dataIntegrationMethodTable/getIncreaseType
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableGetIncreaseType<
  R = Record<string, any>,
  T = Record<string, any>
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataIntegrationMethodTable/getIncreaseType",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 修改任务状态 修改任务状态
 * @url /dataIntegrationMethodTable/mission
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTableMission<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.MissionEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodTable/mission", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询 分页查询
 * @url /dataIntegrationMethodTable/page
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTablePage<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodSqlPageDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodTable/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询工作流实例信息 元数据采集任务实例
 * @url /dataIntegrationMethodTable/pageWorkflowInstance
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodTablePageWorkflowInstance<
  R = API.ResponseDataPageDataWorkflowInstance,
  T = API.ResponseDataPageDataWorkflowInstance
>(
  body: API.DataMetaWorkflowPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataIntegrationMethodTable/pageWorkflowInstance",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}
