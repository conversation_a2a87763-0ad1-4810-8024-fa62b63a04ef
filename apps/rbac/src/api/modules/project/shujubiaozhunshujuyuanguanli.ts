import { basic as request } from "@/api/request";

/**
 * @description 删除目录 目录
 * @url /dataStandard/element/directory/del
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementDirectoryDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/directory/del", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 添加目录 目录
 * @url /dataStandard/element/directory/save
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementDirectorySave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.DirectoryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/directory/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改目录 目录
 * @url /dataStandard/element/directory/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementDirectoryUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.DirectoryDto, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/directory/upd", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 查看目录树 目录
 * @url /dataStandard/element/directoryTree
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementDirectoryTree<
  R = API.ResponseDataListDirectoryDto,
  T = API.ResponseDataListDirectoryDto
>(
  params: API.postDataStandardElementDirectoryTreeParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataStandard/element/directoryTree",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 批量删除数据元 数据元
 * @url /dataStandard/element/element/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/element/batchDel", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据类型下拉 数据元
 * @url /dataStandard/element/element/dataTypeOption
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementDataTypeOption<
  R = API.ResponseDataListOptionVoStringString,
  T = API.ResponseDataListOptionVoStringString
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataStandard/element/element/dataTypeOption",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 默认映射信息 数据元
 * @url /dataStandard/element/element/defFieldMapping
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementDefFieldMapping<
  R = API.ResponseDataListFieldMapping,
  T = API.ResponseDataListFieldMapping
>(
  params: API.postDataStandardElementElementDefFieldMappingParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataStandard/element/element/defFieldMapping",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 删除数据元 数据元
 * @url /dataStandard/element/element/del
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/element/del", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元详情 数据元
 * @url /dataStandard/element/element/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementDetail<
  R = API.ResponseDataDataElementDto,
  T = API.ResponseDataDataElementDto
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataStandard/element/element/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元数据下载 数据元
 * @url /dataStandard/element/element/download
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementDownload<
  R = API.ResponseData,
  T = API.ResponseData
>(
  body: API.DictionaryQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/download", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元数据导入模板 数据元
 * @url /dataStandard/element/element/downloadImportTemplate
 * @method GET
 * <AUTHOR>
 */
export function getDataStandardElementElementDownloadImportTemplate<
  R = any,
  T = any
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>(
    "/dataStandard/element/element/downloadImportTemplate",
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 数据元状态统计 数据元
 * @url /dataStandard/element/element/elementStatusStat
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementElementStatusStat<
  R = API.ResponseDataListStatusStatVo,
  T = API.ResponseDataListStatusStatVo
>(
  body: API.DataElementQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataStandard/element/element/elementStatusStat",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据元信息树 字典管理
 * @url /dataStandard/element/element/elementTree
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementElementTree<
  R = API.ResponseDataListDirectoryDto,
  T = API.ResponseDataListDirectoryDto
>(
  body: API.DictionaryQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/elementTree", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元历史版本 数据元
 * @url /dataStandard/element/element/historyVersion
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementHistoryVersion<
  R = API.ResponseDataListDataElementHistoryVo,
  T = API.ResponseDataListDataElementHistoryVo
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataStandard/element/element/historyVersion",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据元历史版本详情 数据元
 * @url /dataStandard/element/element/historyVersionDetail
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementHistoryVersionDetail<
  R = API.ResponseDataDataElementDto,
  T = API.ResponseDataDataElementDto
>(
  body: API.DataElementQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataStandard/element/element/historyVersionDetail",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据元数据导入 数据元
 * @url /dataStandard/element/element/import
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementImport<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.postDataStandardElementElementImportParams,
  body: Record<string, unknown>,
  file?: File,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  const formData = new FormData();

  if (file) {
    formData.append("file", file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === "object" && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach((f) => formData.append(ele, f || ""));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>("/dataStandard/element/element/import", formData, {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元列表 数据元
 * @url /dataStandard/element/element/list
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementList<
  R = API.ResponseDataPageDataElementListVo,
  T = API.ResponseDataPageDataElementListVo
>(
  body: API.DataElementQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/list", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 添加数据元 数据元
 * @url /dataStandard/element/element/save
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.DataElementDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 添加数据元草稿 数据元
 * @url /dataStandard/element/element/saveDraft
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementSaveDraft<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.DataElementDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/saveDraft", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改数据元 数据元
 * @url /dataStandard/element/element/update
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementUpdate<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.DataElementDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataStandard/element/element/update", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据元 上线/下线 数据元
 * @url /dataStandard/element/element/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataStandardElementElementUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  params: API.postDataStandardElementElementUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataStandard/element/element/updStatus",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
