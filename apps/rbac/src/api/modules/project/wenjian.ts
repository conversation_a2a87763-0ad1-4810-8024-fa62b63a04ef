import { basic as request } from '@/api/request';

/**
 * @description 图片上传
 * @url /file/upload
 * @method POST
 * <AUTHOR>
 */
export function postFileUpload<R = API.ResponseData, T = API.ResponseData>(
  body: Record<string, unknown>,
  file?: File,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>('/file/upload', formData, {
    ...(options || {}),
  });
}

/**
 * @description 单张图片上传，大于5M会压缩
 * @url /file/uploadPhoto
 * @method POST
 * <AUTHOR>
 */
export function postFileUploadPhoto<R = API.ResponseData, T = API.ResponseData>(
  body: Record<string, unknown>,
  file?: File,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>('/file/uploadPhoto', formData, {
    ...(options || {}),
  });
}

/**
 * @description 单张图片上传，大于5M会压缩
 * @url /file/uploadPhoto/open
 * @method POST
 * <AUTHOR>
 */
export function postFileUploadPhotoOpen<R = API.ResponseData, T = API.ResponseData>(
  body: Record<string, unknown>,
  file?: File,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (Array.isArray(item)) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request.Post<R, T>('/file/uploadPhoto/open', formData, {
    ...(options || {}),
  });
}

/**
 * @description 多张图片上传，大于5M会压缩
 * @url /file/uploadPhotos
 * @method POST
 * <AUTHOR>
 */
export function postFileUploadPhotos<R = API.ResponseData, T = API.ResponseData>(
  params: API.postFileUploadPhotosParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/file/uploadPhotos',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
