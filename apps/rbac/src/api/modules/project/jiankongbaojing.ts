import { basic as request } from '@/api/request';

/**
 * @description 添加报警人信息
 * @url /monitor/email/add
 * @method POST
 * <AUTHOR>
 */
export function postMonitorEmailAdd<R = Record<string, any>, T = Record<string, any>>(
  body: API.MonitorUserSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/monitor/email/add', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除报警人信息
 * @url /monitor/email/delete/${param0}
 * @method POST
 * <AUTHOR>
 */
export function postMonitorEmailDeleteById<R = Record<string, any>, T = Record<string, any>>(
  params: API.postMonitorEmailDeleteByIdParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  const { id: param0, ...queryParams } = params;
  return request.Post<R, T>(
    `/monitor/email/delete/${param0}`,
    {},
    {
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/**
 * @description 报警人信息查询
 * @url /monitor/email/findList
 * @method GET
 * <AUTHOR>
 */
export function getMonitorEmailFindList<R = Record<string, any>, T = Record<string, any>>(
  params: API.getMonitorEmailFindListParams,
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/monitor/email/findList', {
    params: {
      ...params,
      monitorUserDO: undefined,
      ...params['monitorUserDO'],
    },
    ...(options || {}),
  });
}

/**
 * @description 编辑报警人信息
 * @url /monitor/email/update
 * @method POST
 * <AUTHOR>
 */
export function postMonitorEmailUpdate<R = Record<string, any>, T = Record<string, any>>(
  body: API.MonitorUserSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/monitor/email/update', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}
