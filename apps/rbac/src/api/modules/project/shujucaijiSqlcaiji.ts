import { basic as request } from "@/api/request";

/**
 * @description 数据采集-sql采集新增 数据采集-sql采集新增
 * @url /dataIntegrationMethodSql/add
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodSqlAddDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodSql/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 创建sql预览
 * @url /dataIntegrationMethodSql/createPreview
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlCreatePreview<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.DataIntegrationMethodSqlPreviewDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodSql/createPreview", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-sql采集删除 数据采集-sql采集删除
 * @url /dataIntegrationMethodSql/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodSql/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-sql采集详情 数据采集-sql采集详情
 * @url /dataIntegrationMethodSql/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlDetail<
  R = Record<string, any>,
  T = Record<string, any>
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodSql/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据采集-sql采集编辑 数据采集-sql采集编辑
 * @url /dataIntegrationMethodSql/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlEdit<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodSqlEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodSql/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改任务状态 修改任务状态
 * @url /dataIntegrationMethodSql/mission
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlMission<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.MissionEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodSql/mission", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询 分页查询
 * @url /dataIntegrationMethodSql/page
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlPage<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodSqlPageDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodSql/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询工作流实例信息 元数据采集任务实例
 * @url /dataIntegrationMethodSql/pageWorkflowInstance
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodSqlPageWorkflowInstance<
  R = API.ResponseDataPageDataWorkflowInstance,
  T = API.ResponseDataPageDataWorkflowInstance
>(
  body: API.DataMetaWorkflowPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataIntegrationMethodSql/pageWorkflowInstance",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}
