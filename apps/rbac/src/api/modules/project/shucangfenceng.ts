import { basic as request } from "@/api/request";

/**
 * @description 新增数仓分层 新增数仓分层
 * @url /dataWarehouse/layer/add
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataWarehouseLayerSave,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataWarehouse/layer/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数仓分层列表 所有数仓分层数据，按类区分
 * @url /dataWarehouse/layer/all
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerAll<
  R = API.ResponseDataListDataWarehouseLayerMap,
  T = API.ResponseDataListDataWarehouseLayerMap
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataWarehouse/layer/all",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 删除数仓分层 删除数仓分层
 * @url /dataWarehouse/layer/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataWarehouse/layer/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 新增数仓分层规范 新增数仓分层规范
 * @url /dataWarehouse/layer/rule/add
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerRuleAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataWarehouseRuleSave,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataWarehouse/layer/rule/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 删除数仓分层规范 删除数仓分层规范,id为数仓分层id
 * @url /dataWarehouse/layer/rule/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerRuleDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataWarehouse/layer/rule/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改数仓分层规范 修改数仓分层规范
 * @url /dataWarehouse/layer/rule/update
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerRuleUpdate<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataWarehouseRuleSave,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataWarehouse/layer/rule/update", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数层分层规范列表
 * @url /dataWarehouse/layer/rules
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerRules<
  R = API.ResponseDataListDataWarehouseRule,
  T = API.ResponseDataListDataWarehouseRule
>(body: API.Layerid, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataWarehouse/layer/rules", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改数仓分层 修改数仓分层
 * @url /dataWarehouse/layer/update
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseLayerUpdate<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataWarehouseLayerSave,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataWarehouse/layer/update", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
