import { basic as request } from "@/api/request";

/**
 * @description 应用日志分页列表
 * @url /app/list
 * @method POST
 * <AUTHOR>
 */
export function postAppList<R = API.AppLog[], T = API.AppLog[]>(
  body: API.AppLogFileVo,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/app/list", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description systemlog日志--按数据创建时间区间查询
 * @url /system/list
 * @method POST
 * <AUTHOR>
 */
export function postSystemList<R = API.SystemLogDto, T = API.SystemLogDto>(
  body: API.SystemLogQueryInfo,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/system/list", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
