import { basic as request } from "@/api/request";

/**
 * @description 测试应用配置接口sql 测试应用配置接口sql
 * @url /apiData/appApiSql/test
 * @method POST
 * <AUTHOR>
 */
export function postApiDataAppApiSqlTest<
  R = API.yingyongpeizhijiekousqlceshijieguo,
  T = API.yingyongpeizhijiekousqlceshijieguo
>(
  body: API.AppApiSqlTestParam,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/apiData/appApiSql/test", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 通过api接口id,查询对应版本数据列表 通过api接口id,查询对应版本数据列表
 * @url /apiData/checkUrl
 * @method GET
 * <AUTHOR>
 */
export function getApiDataCheckUrl<R = API.ResponseData, T = API.ResponseData>(
  params: API.getApiDataCheckUrlParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/checkUrl", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 编辑api接口 编辑api接口
 * @url /apiData/edit
 * @method POST
 * <AUTHOR>
 */
export function postApiDataEdit<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiVO,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/apiData/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 通过api接口id,查询对应数据 通过api接口id,查询对应数据
 * @url /apiData/find
 * @method GET
 * <AUTHOR>
 */
export function getApiDataFind<R = API.ResponseData, T = API.ResponseData>(
  params: API.getApiDataFindParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/find", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 通过接口原始id和版本号查询查询对应接口 通过接口原始id和版本号查询查询对应接口
 * @url /apiData/find/byParentIdAndVersion
 * @method GET
 * <AUTHOR>
 */
export function getApiDataFindByParentIdAndVersion<
  R = API.ApiInfoVo,
  T = API.ApiInfoVo
>(
  params: API.getApiDataFindByParentIdAndVersionParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/find/byParentIdAndVersion", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据接口id获取相关所有接口信息 根据接口id获取相关所有接口信息
 * @url /apiData/findAllByApiId
 * @method GET
 * <AUTHOR>
 */
export function getApiDataFindAllByApiId<
  R = API.DataApiSaveDO,
  T = API.DataApiSaveDO
>(
  params: API.getApiDataFindAllByApiIdParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/findAllByApiId", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description api接口列表查询 api接口列表查询
 * @url /apiData/findList
 * @method POST
 * <AUTHOR>
 */
export function postApiDataFindList<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiDO,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/apiData/findList", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 通过api接口id,查询对应版本数据列表 通过api接口id,查询对应版本数据列表
 * @url /apiData/findVersionList
 * @method GET
 * <AUTHOR>
 */
export function getApiDataFindVersionList<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.getApiDataFindVersionListParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/findVersionList", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 获取包含接口的主题域及接口列表
 * @url /apiData/getApiDomainList
 * @method GET
 * <AUTHOR>
 */
export function getApiDataGetApiDomainList<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.getApiDataGetApiDomainListParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/getApiDomainList", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description API入出参下拉框可展示参数
 * @url /apiData/getApiInOut
 * @method GET
 * <AUTHOR>
 */
export function getApiDataGetApiInOut<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.getApiDataGetApiInOutParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/getApiInOut", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 根据数据板块，主题域id获取接口列表
 * @url /apiData/getApiList
 * @method POST
 * <AUTHOR>
 */
export function postApiDataGetApiList<
  R = API.ResponseDataPageDataDataApiListVo,
  T = API.ResponseDataPageDataDataApiListVo
>(
  body: API.DataApiListDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/apiData/getApiList", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 获取包含接口的数据板块
 * @url /apiData/getApiProject
 * @method GET
 * <AUTHOR>
 */
export function getApiDataGetApiProject<
  R = API.ResponseData,
  T = API.ResponseData
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>("/apiData/getApiProject", {
    ...(options || {}),
  });
}

/**
 * @description 根据数据板块，主题域id获取数据表信息
 * @url /apiData/getTableMsg
 * @method GET
 * <AUTHOR>
 */
export function getApiDataGetTableMsg<
  R = API.ResponseData,
  T = API.ResponseData
>(
  params: API.getApiDataGetTableMsgParams,
  options?: Parameters<typeof request.Get<R, T>>[1]
) {
  return request.Get<R, T>("/apiData/getTableMsg", {
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/**
 * @description 通过api接口id,删除对应数据 通过api接口id,删除对应数据
 * @url /apiData/remove
 * @method POST
 * <AUTHOR>
 */
export function postApiDataRemove<R = API.ResponseData, T = API.ResponseData>(
  params: API.postApiDataRemoveParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/apiData/remove",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 添加api接口 添加api接口
 * @url /apiData/save
 * @method POST
 * <AUTHOR>
 */
export function postApiDataSave<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiSaveDO,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/apiData/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
