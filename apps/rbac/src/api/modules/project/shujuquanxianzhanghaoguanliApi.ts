import { basic as request } from '@/api/request';

/**
 * @description 批量新增接口授权
 * @url /apiUser/addAuthorizationApi
 * @method POST
 * <AUTHOR>
 */
export function postApiUserAddAuthorizationApi<R = API.ResponseData, T = API.ResponseData>(
  body: API.AuthDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/addAuthorizationApi', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 批量新增用户授权
 * @url /apiUser/addAuthorizationUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserAddAuthorizationUser<R = API.ResponseData, T = API.ResponseData>(
  body: API.AuthUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/addAuthorizationUser', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 新增/编辑账号
 * @url /apiUser/addUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserAddUser<R = API.ResponseData, T = API.ResponseData>(
  body: API.EdusoaAppUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/addUser', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 生成密钥
 * @url /apiUser/createClient
 * @method GET
 * <AUTHOR>
 */
export function getApiUserCreateClient<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/apiUser/createClient', {
    ...(options || {}),
  });
}

/**
 * @description 删除账号
 * @url /apiUser/deleteUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserDeleteUser<R = API.ResponseData, T = API.ResponseData>(
  params: API.postApiUserDeleteUserParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/apiUser/deleteUser',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 根据数据板块主题域获取未授权接口
 * @url /apiUser/getApiByProjectDomain
 * @method POST
 * <AUTHOR>
 */
export function postApiUserGetApiByProjectDomain<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/getApiByProjectDomain', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页获取用户已授权接口
 * @url /apiUser/getApiMsg
 * @method POST
 * <AUTHOR>
 */
export function postApiUserGetApiMsg<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/getApiMsg', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 根据数据板块主题域获取用户已授权接口
 * @url /apiUser/getUserApiByProjectDomain
 * @method POST
 * <AUTHOR>
 */
export function postApiUserGetUserApiByProjectDomain<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/getUserApiByProjectDomain', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 根据接口获取是否授权用户(可模糊查询)
 * @url /apiUser/getUserIsAuthorization
 * @method POST
 * <AUTHOR>
 */
export function postApiUserGetUserIsAuthorization<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataApiUserDo,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/getUserIsAuthorization', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询账号(可根据姓名厂家模糊查询和角色筛选)
 * @url /apiUser/getUserMsg
 * @method POST
 * <AUTHOR>
 */
export function postApiUserGetUserMsg<R = API.ResponseData, T = API.ResponseData>(
  body: API.EdusoaAppUserDO,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/getUserMsg', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 批量取消接口授权
 * @url /apiUser/revokeApi
 * @method POST
 * <AUTHOR>
 */
export function postApiUserRevokeApi<R = API.ResponseData, T = API.ResponseData>(
  body: API.AuthDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/revokeApi', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 批量取消用户授权
 * @url /apiUser/revokeUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserRevokeUser<R = API.ResponseData, T = API.ResponseData>(
  body: API.AuthUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/apiUser/revokeUser', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 启用账号
 * @url /apiUser/startUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserStartUser<R = API.ResponseData, T = API.ResponseData>(
  params: API.postApiUserStartUserParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/apiUser/startUser',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/**
 * @description 停用账号
 * @url /apiUser/stopUser
 * @method POST
 * <AUTHOR>
 */
export function postApiUserStopUser<R = API.ResponseData, T = API.ResponseData>(
  params: API.postApiUserStopUserParams,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>(
    '/apiUser/stopUser',
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
