import { basic as request } from "@/api/request";

/**
 * @description 删除元数据 元数据注册
 * @url /data/meta/table/del
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/data/meta/table/del", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 查询元数据详情 元数据注册
 * @url /data/meta/table/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableDetail<
  R = API.ResponseDataDataMetaTableVo,
  T = API.ResponseDataDataMetaTableVo
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/data/meta/table/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 注册元数据excel下载 元数据注册
 * @url /data/meta/table/download
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableDownload<R = any, T = any>(
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/data/meta/table/download",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 分页查询元数据 元数据注册
 * @url /data/meta/table/page
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTablePage<
  R = API.ResponseDataPageDataDataMetaTablePageVo,
  T = API.ResponseDataPageDataDataMetaTablePageVo
>(
  body: API.DataMetaTablePage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/table/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 获取已注册发布的元数据信息 根据数据源id获取已注册发布的元数据信息
 * @url /data/meta/table/publish
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTablePublish<
  R = API.ResponseDataListDataMetaTablePublish,
  T = API.ResponseDataListDataMetaTablePublish
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/data/meta/table/publish", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 注册元数据信息 元数据注册
 * @url /data/meta/table/save
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableSave<
  R = API.ResponseData,
  T = API.ResponseData
>(
  body: API.DataMetaTableDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/table/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 上线(发布数据资产)/下线(取消发布数据资产) 元数据注册
 * @url /data/meta/table/setStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableSetStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.MetaStatus, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/data/meta/table/setStatus", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 获取元数据版本信息 元数据注册
 * @url /data/meta/table/versions
 * @method POST
 * <AUTHOR>
 */
export function postDataMetaTableVersions<
  R = API.ResponseDataPageDataTableVersionVo,
  T = API.ResponseDataPageDataTableVersionVo
>(
  body: API.TableVersionPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/data/meta/table/versions", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
