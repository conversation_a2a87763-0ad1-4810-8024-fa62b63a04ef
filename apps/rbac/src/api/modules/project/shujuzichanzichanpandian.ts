import { basic as request } from "@/api/request";

/**
 * @description 数据资产分析 数据资产分析
 * @url /assetinventory/analysis
 * @method POST
 * <AUTHOR>
 */
export function postAssetinventoryAnalysis<
  R = API.ResponseData,
  T = API.ResponseData
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/assetinventory/analysis",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 数据资产概览 数据资产概览
 * @url /assetinventory/detail
 * @method POST
 * <AUTHOR>
 */
export function postAssetinventoryDetail<
  R = API.ResponseData,
  T = API.ResponseData
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/assetinventory/detail",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 数据质量分析 数据质量分析
 * @url /assetinventory/quality
 * @method POST
 * <AUTHOR>
 */
export function postAssetinventoryQuality<
  R = API.ResponseDataListDataQualityTaskResultReport,
  T = API.ResponseDataListDataQualityTaskResultReport
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/assetinventory/quality",
    {},
    {
      ...(options || {}),
    }
  );
}
