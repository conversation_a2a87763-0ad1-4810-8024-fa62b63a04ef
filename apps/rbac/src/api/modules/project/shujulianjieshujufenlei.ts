import { basic as request } from "@/api/request";

/**
 * @description 数据分类，新增
 * @url /sort/add
 * @method POST
 * <AUTHOR>
 */
export function postSortAdd<R = Record<string, any>, T = Record<string, any>>(
  body: API.shujufenleicanshuxinzeng,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/sort/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据分类，删除
 * @url /sort/dlt
 * @method POST
 * <AUTHOR>
 */
export function postSortDlt<R = Record<string, any>, T = Record<string, any>>(
  params: API.postSortDltParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/sort/dlt",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据分类，修改
 * @url /sort/edit
 * @method POST
 * <AUTHOR>
 */
export function postSortEdit<R = Record<string, any>, T = Record<string, any>>(
  body: API.shujufenleicanshubianji,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/sort/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据分类，单个查询
 * @url /sort/find
 * @method POST
 * <AUTHOR>
 */
export function postSortFind<R = Record<string, any>, T = Record<string, any>>(
  params: API.postSortFindParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/sort/find",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据分类列表，详细列表
 * @url /sort/list
 * @method POST
 * <AUTHOR>
 */
export function postSortList<R = Record<string, any>, T = Record<string, any>>(
  params: API.postSortListParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/sort/list",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 数据分类列表，分页列表
 * @url /sort/page
 * @method POST
 * <AUTHOR>
 */
export function postSortPage<R = Record<string, any>, T = Record<string, any>>(
  body: API.DataSortPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/sort/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 数据分类列表，简易列表
 * @url /sort/simplelist
 * @method POST
 * <AUTHOR>
 */
export function postSortSimplelist<
  R = Record<string, any>,
  T = Record<string, any>
>(
  params: API.postSortSimplelistParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/sort/simplelist",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
