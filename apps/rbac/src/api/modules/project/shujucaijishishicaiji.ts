import { basic as request } from "@/api/request";

/**
 * @description 采集新增 采集新增
 * @url /dataIntegrationMethodRealTime/add
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeAdd<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodRealTimeAddDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/add", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 创建预览
 * @url /dataIntegrationMethodRealTime/createPreview
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeCreatePreview<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.DataIntegrationMethodRealTimePreviewDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataIntegrationMethodRealTime/createPreview",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 采集删除 采集删除
 * @url /dataIntegrationMethodRealTime/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/delete", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 采集详情 采集详情
 * @url /dataIntegrationMethodRealTime/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeDetail<
  R = Record<string, any>,
  T = Record<string, any>
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 采集编辑 采集编辑
 * @url /dataIntegrationMethodRealTime/edit
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeEdit<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodRealTimeEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/edit", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改任务状态 修改任务状态
 * @url /dataIntegrationMethodRealTime/mission
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimeMission<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.MissionEditDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/mission", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询 分页查询
 * @url /dataIntegrationMethodRealTime/page
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimePage<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject
>(
  body: API.DataIntegrationMethodSqlPageDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataIntegrationMethodRealTime/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 分页查询工作流实例信息
 * @url /dataIntegrationMethodRealTime/pageWorkflowInstance
 * @method POST
 * <AUTHOR>
 */
export function postDataIntegrationMethodRealTimePageWorkflowInstance<
  R = API.ResponseDataPageDataWorkflowInstance,
  T = API.ResponseDataPageDataWorkflowInstance
>(
  body: API.DataMetaWorkflowPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataIntegrationMethodRealTime/pageWorkflowInstance",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}
