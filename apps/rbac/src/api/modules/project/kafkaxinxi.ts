import { basic as request } from "@/api/request";

/**
 * @description 逻辑删除 kafka 信息 (by id)
 * @url /info/kafka/del
 * @method POST
 * <AUTHOR>
 */
export function postInfoKafkaDel<
  R = Record<string, any>,
  T = Record<string, any>
>(
  params: API.postInfoKafkaDelParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/info/kafka/del",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 获取 kafka 信息
 * @url /info/kafka/get
 * @method POST
 * <AUTHOR>
 */
export function postInfoKafkaGet<
  R = Record<string, any>,
  T = Record<string, any>
>(
  params: API.postInfoKafkaGetParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/info/kafka/get",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 获取全部 kafka 信息
 * @url /info/kafka/getAll
 * @method POST
 * <AUTHOR>
 */
export function postInfoKafkaGetAll<
  R = Record<string, any>,
  T = Record<string, any>
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/info/kafka/getAll",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description [添加/更新] kafka 信息
 * @url /info/kafka/upsert
 * @method POST
 * <AUTHOR>
 */
export function postInfoKafkaUpsert<
  R = Record<string, any>,
  T = Record<string, any>
>(
  body: API.KafkaInfoSaveDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/info/kafka/upsert", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
