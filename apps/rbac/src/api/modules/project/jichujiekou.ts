import { basic as request } from "@/api/request";

/**
 * @description 分页查询用户信息 用户下拉框
 * @url /base/user/pageQuery
 * @method POST
 * <AUTHOR>
 */
export function postBaseUserPageQuery<
  R = API.ResponseDataPageDataPubUserDto,
  T = API.ResponseDataPageDataPubUserDto
>(
  body: API.BasePubUserDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/base/user/pageQuery", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
