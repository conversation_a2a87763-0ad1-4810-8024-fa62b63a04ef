import { basic as request } from "@/api/request";

/**
 * @description 批量删除加密算法 加密算法
 * @url /dataSecurity/encryptionAlgorithm/batchDel
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmBatchDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: number[], options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataSecurity/encryptionAlgorithm/batchDel",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 删除加密算法 加密算法
 * @url /dataSecurity/encryptionAlgorithm/del
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmDel<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSecurity/encryptionAlgorithm/del", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 加密算法详情 加密算法
 * @url /dataSecurity/encryptionAlgorithm/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmDetail<
  R = API.ResponseDataEncryptionAlgorithmDto,
  T = API.ResponseDataEncryptionAlgorithmDto
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>("/dataSecurity/encryptionAlgorithm/detail", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 加密方式下拉 加密算法
 * @url /dataSecurity/encryptionAlgorithm/encryptionMethod/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmEncryptionMethodOptions<
  R = API.ResponseDataListEncryptionMethodVo,
  T = API.ResponseDataListEncryptionMethodVo
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataSecurity/encryptionAlgorithm/encryptionMethod/options",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 加密算法下拉(新) 加密算法
 * @url /dataSecurity/encryptionAlgorithm/newOptions
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmNewOptions<
  R = API.ResponseDataListEncryptionAlgorithmNewListVo,
  T = API.ResponseDataListEncryptionAlgorithmNewListVo
>(options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>(
    "/dataSecurity/encryptionAlgorithm/newOptions",
    {},
    {
      ...(options || {}),
    }
  );
}

/**
 * @description 加密算法下拉 加密算法
 * @url /dataSecurity/encryptionAlgorithm/options
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmOptions<
  R = API.ResponseDataListOptionVoStringLong,
  T = API.ResponseDataListOptionVoStringLong
>(
  body: API.EncryptionAlgorithmQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSecurity/encryptionAlgorithm/options", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 加密算法分页列表 加密算法
 * @url /dataSecurity/encryptionAlgorithm/pageList
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmPageList<
  R = API.ResponseDataPageEncryptionAlgorithmListVo,
  T = API.ResponseDataPageEncryptionAlgorithmListVo
>(
  body: API.EncryptionAlgorithmQuery,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataSecurity/encryptionAlgorithm/pageList",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 添加加密算法 加密算法
 * @url /dataSecurity/encryptionAlgorithm/save
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmSave<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.EncryptionAlgorithmDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSecurity/encryptionAlgorithm/save", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改加密算法 加密算法
 * @url /dataSecurity/encryptionAlgorithm/upd
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmUpd<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  body: API.EncryptionAlgorithmDto,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataSecurity/encryptionAlgorithm/upd", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}

/**
 * @description 修改加密算法状态 加密算法
 * @url /dataSecurity/encryptionAlgorithm/updStatus
 * @method POST
 * <AUTHOR>
 */
export function postDataSecurityEncryptionAlgorithmUpdStatus<
  R = API.ResponseDataBoolean,
  T = API.ResponseDataBoolean
>(
  params: API.postDataSecurityEncryptionAlgorithmUpdStatusParams,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataSecurity/encryptionAlgorithm/updStatus",
    {},
    {
      params: {
        ...params,
      },
      ...(options || {}),
    }
  );
}
