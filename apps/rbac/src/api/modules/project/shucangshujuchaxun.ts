import { basic as request } from "@/api/request";

/**
 * @description 创建即席查询任务 返回结果作为结果查询参数，即席查询结果接口需要定时循环调用，直到返回数据为止
 * @url /dataWarehouse/modeldata/adhoc/page/create
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModeldataAdhocPageCreate<
  R = API.ResponseDataAdhocQueryResultPage,
  T = API.ResponseDataAdhocQueryResultPage
>(
  body: API.AdhocQueryPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataWarehouse/modeldata/adhoc/page/create",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 即席查询结果 返回结果作为结果查询参数，若返回code=1,说明结果还在生成，需要重复调用此接口
 * @url /dataWarehouse/modeldata/adhoc/page/result
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModeldataAdhocPageResult<
  R = API.ResponseDataAdhocPageDataMapStringObject,
  T = API.ResponseDataAdhocPageDataMapStringObject
>(
  body: API.AdhocQueryResultPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>(
    "/dataWarehouse/modeldata/adhoc/page/result",
    body,
    {
      headers: {
        "Content-Type": "application/json",
      },
      ...(options || {}),
    }
  );
}

/**
 * @description 所有数据源 所有数据源，下拉框选项
 * @url /dataWarehouse/modeldata/dataSource/all
 * @method GET
 * <AUTHOR>
 */
export function getDataWarehouseModeldataDataSourceAll<
  R = API.ResponseDataListDataSourceSimpleDto,
  T = API.ResponseDataListDataSourceSimpleDto
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>("/dataWarehouse/modeldata/dataSource/all", {
    ...(options || {}),
  });
}

/**
 * @description 主数据查询 主数据查询
 * @url /dataWarehouse/modeldata/mainData/page
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModeldataMainDataPage<
  R = API.ResponseDataPageDataMapStringObject,
  T = API.ResponseDataPageDataMapStringObject
>(
  body: API.MainDataQueryPage,
  options?: Parameters<typeof request.Post<R, T>>[2]
) {
  return request.Post<R, T>("/dataWarehouse/modeldata/mainData/page", body, {
    headers: {
      "Content-Type": "application/json",
    },
    ...(options || {}),
  });
}
