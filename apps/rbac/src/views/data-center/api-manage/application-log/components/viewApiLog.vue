<script setup lang="ts">
import { useToggle } from '@vueuse/core';
import type { LogItem } from '../types';

const [open, setOpen] = useToggle(false);

const log = ref('');

function onOpen(data: LogItem) {
  log.value = data.stackTrace;
  setOpen(true);
}

const [loading, setLoading] = useToggle(false);

defineExpose({
  open: onOpen,
});
</script>

<template>
  <a-drawer
    v-model:open="open"
    title="查看日志"
    placement="right"
    :width="800"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
  >
    <div v-if="log" class="log-content p-[16px]">
      <a-textarea v-model:value="log" autosize :bordered="false" :disabled="true" />
    </div>
    <div v-else class="p-[16px]">
      <a-empty description="暂无日志" />
    </div>
  </a-drawer>
</template>

<style scoped lang="scss">
  .log-content {
    background-color: #38496c;
    border-radius: 6px;

    :deep(.ant-input-disabled) {
      color: #fff;
    }
  }
</style>
