<script setup lang="ts">
import { computed, onMounted } from 'vue';
import VueJsonPretty from 'vue-json-pretty';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'DocDetailConst',
});

const router = useRouter();

// 响应式数据
const docView = computed(() => {
  return {
    id: 12,
    name: '获取AccessToken接口', // 接口名称
    inputColumns: [
      { title: '参数名', dataIndex: 'paraName', key: 'paraName', align: 'center', width: 250 },
      { title: '位置', dataIndex: 'position', key: 'position', align: 'center', width: 250 },
      { title: '参数类型', dataIndex: 'paraType', key: 'paraType', align: 'center', width: 250 },
      { title: '描述', dataIndex: 'paraDesc', key: 'paraDesc', align: 'center' },
    ],
    outPutColumns: [
      { title: '参数名', dataIndex: 'paraName', key: 'paraName', align: 'center', width: 250 },
      { title: '参数类型', dataIndex: 'paraType', key: 'paraType', align: 'center', width: 250 },
      { title: '描述', dataIndex: 'paraDesc', key: 'paraDesc', align: 'center' },
    ],
    stepList: [ // 步骤顺序
      {
        stepName: '',
        // 简介信息，每一项为一行
        describeList: [
          '请求方式：POST application/x-www-form-urlencoded;charset=utf-8',
          `请求链接：http://115.223.57.34:6099/api/svc-openapi/auth/app/api/token`,
        ],
        // 入参列表
        inputList: [
          { paraName: 'grant_type', paraType: 'String', paraDesc: '填写默认值：client_credentials', position: 'header' },
          { paraName: 'scope', paraType: 'String', paraDesc: '填写默认值：all', position: 'header' },
          { paraName: 'client_id', paraType: 'String', paraDesc: '密钥id', position: 'header' },
          { paraName: 'timestamp', paraType: 'String', paraDesc: '时间戳', position: 'header' },
          { paraName: 'signature', paraType: 'String', paraDesc: `将(client_id+client_secret+timestamp)用MD5加密生成后的值。
          示例
          client_id: da0920181df149699c76b2f92b956ccf;
          client_secret: 7abf604963184bcc96648c7466f34038;
          timestamp: 1753429730894(入参中的timestamp);
          加密后的值为: 3d06bc4ccad0a909dc1a71ea82c67d19`, position: 'header' },
        ],
        // 入参格式
        inputJson: '',
        // 出参列表
        outputList: [
          { paraName: 'access_token', paraType: 'String', paraDesc: '获取到的凭证' },
          { paraName: 'expires_in', paraType: 'long', paraDesc: '有效期，单位：秒，默认值3600' },
          { paraName: 'token_type', paraType: 'String', paraDesc: 'token类型：默认Bearer' },
          { paraName: 'scope', paraType: 'String', paraDesc: '权限范围，默认：all' },
        ],
        // 出参格式
        outputJson: '{"code": 0,"message": "成功","accessToken": {"scope": "all","access_token": "at_dbed0576534d4d0fb242acd60ad62645","expires_in": 3600,"token_type": "Bearer"}}',
        // 举例
        example: '',
      },
    ],
  };
});

function goBack() {
  router.back();
}

// 生命周期
onMounted(() => {
});
</script>

<template>
  <div class="doc_detail_const w-full h-full py-29px px-40px flex flex-col">
    <div class="content">
      <div class="doc-view">
        <div class="flex justify-between items-center">
          <div class="doc-title">
            {{ docView.name }}
          </div>
          <div>
            <a-button type="primary" @click="goBack">
              返回
            </a-button>
          </div>
        </div>

        <div v-for="(item, index) in docView.stepList" :key="index" style="margin-top: 10px;">
          <div>
            <div class="doc-h1">
              <!-- {{ `${index + 1}. ${item.stepName}` }} -->
            </div>
            <div v-for="(i, ind) in item.describeList" :key="ind" class="doc-content">
              {{ i }}
            </div>
            <!-- 入参参数 -->
            <div v-if="item.inputList.length !== 0">
              <div class="view-title">
                入参参数：
              </div>
              <a-table :columns="docView.inputColumns" :data-source="item.inputList" bordered :pagination="false" />
            </div>
            <!-- 入参格式 -->
            <div v-if="item.inputJson">
              <div class="view-title">
                入参格式：
              </div>
              <div class="json-bg">
                <VueJsonPretty path="data" :data="item.inputJson" :deep="10" />
              </div>
            </div>
            <!-- 出参参数 -->
            <div v-if="item.outputList.length !== 0">
              <div class="view-title">
                出参参数：
              </div>
              <a-table :columns="docView.outPutColumns" :data-source="item.outputList" bordered :pagination="false" />
            </div>
            <!-- 出参格式 -->
            <div v-if="item.outputJson">
              <div class="view-title">
                出参格式：
              </div>
              <div class="json-bg">
                <VueJsonPretty path="data" :data="JSON.parse(item.outputJson)" :deep="10" />
              </div>
            </div>
            <div v-if="item.example">
              <div class="view-title">
                举例：
              </div>
              <div class="doc-content">
                {{ item.example }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.doc_detail_const {
  .content {
    display: flex;
    flex: 1;
    padding: 30px;
    background-color: #fff;
    // justify-content: center;
  }
}

.doc-view {
  flex: 1;
  // width: 100%;
  color: #5b6167;

  .doc-title {
    margin-top: 20px;
    font-size: 22px;
    font-weight: 500;
    color: #151e26;
  }

  .doc-text {
    margin-top: 8px;
    margin-bottom: 20px;
    color: #b8bbbd;
  }

  .doc-h1 {
    display: flex;
    align-items: center;
    height: 40px;
    margin-top: 10px;
    font-size: 16px;
    font-weight: 500;
    color: #151e26;
    border-bottom: 1px solid #e7e8e9;
  }

  .doc-h2 {
    margin-top: 10px;
    font-size: 20px;
    font-weight: 500;
    color: #151e26;
  }

  .doc-content {
    margin-top: 7px;
  }

  .doc-h3 {
    display: flex;
    align-items: center;
    height: 40px;
    margin-top: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #151e26;
  }
}

.view-title {
  margin: 10px 0;
  margin-top: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #0375e6;
}

pre {
  word-wrap: break-word;
  white-space: pre-wrap;
}

.decrypt-line {
  padding: 10px;
  line-height: 30px;
  color: #fff;
  background-color: rgb(0 0 0 / 80%);
}

.json-bg {
  padding: 10px;
  color: #fff;
  background-color: rgb(0 0 0 / 80%);
}

// // 表格样式
// /deep/.ivu-table th {
//   position: relative;
//   height: 53px;
//   background-color: #f7f7f7;
// }

// /deep/.ivu-table th::before {
//   width: 0;
//   content: "";
// }

// /deep/.ivu-table th:first-of-type::before {
//   content: "";
// }
</style>
