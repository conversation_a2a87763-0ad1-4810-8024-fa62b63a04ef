<script setup lang="tsx">
import type { Rule } from 'ant-design-vue/es/form';
import type { FormExpose } from 'ant-design-vue/es/form/Form';
import { useToggle } from '@vueuse/core';
import { message } from 'ant-design-vue';
import CustomDrawer from '@/components/CustomDrawer/index.vue';

defineOptions({
  name: 'AddDrawer',
});
// const props = defineProps<{
//   dataProjectId: string | number
// }>();
const emit = defineEmits(['submit']);
const [open, setOpen] = useToggle(false);

const formRef = ref<FormExpose>();
const form = ref({
  // username: '',
  // password: '',
  // password_repeat: '',
  name: '',
  phone: '',
  company: '',
  role: '',
  clientId: '',
  clientSecret: '',
});

// function passCheck(_: unknown, value: string) {
//   if (!value) {
//     return Promise.reject(new Error('请输入确认密码'));
//   }
//   if (form.value.password !== value) {
//     return Promise.reject(new Error('两次密码不一致，请重新输入'));
//   }
//   return Promise.resolve();
// }

const rules: Record<string, Rule[]> = {
  // username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  // password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  // password_repeat: [
  //   { required: true, message: '请输入确认密码', trigger: 'blur' },
  //   { validator: passCheck, trigger: 'blur' },
  // ],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  company: [{ required: true, message: '请输入所属厂家', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  clientId: [{ required: false, message: '请输入clientId', trigger: 'blur' }],
  clientSecret: [{ required: false, message: '请输入clientSecret', trigger: 'blur' }],
};

const typeOptions: Ref<{ label?: string, value?: string }[] | undefined> = ref([]);
function getRoleType() {
  getDictItemList({ dictCode: 'roleType' }).then((res: API.ResponseDataListDictItemVo) => {
    typeOptions.value = res.data?.map((e) => {
      return {
        label: e.dictLabel,
        value: e.dictValue,
      };
    });
  });
}
function getClient() {
  getApiUserCreateClient().then((res) => {
    form.value.clientId = res.data.clientId;
    form.value.clientSecret = res.data.clientKey;
  });
}

const title = ref('');
async function onOpen(key: string, record: any) {
  title.value = key;
  if (record && record.id) {
    form.value = { ...record, password_repeat: record.password };
  }
  else {
    getClient();
  }
  getRoleType();
  setOpen(true);
}

const [loading, setLoading] = useToggle(false);

function onSubmit() {
  formRef.value?.validate().then(() => {
    setLoading(true);
    const params = {
      ...form.value,
    };
    postApiUserAddUser(params as any).then(() => {
      setOpen(false);
      message.success('保存成功');
      emit('submit');
      formRef.value?.resetFields();
    }).catch(() => {
      setLoading(false);
    });
  });
}

function close() {
  setOpen(false);
}

defineExpose({
  open: onOpen,
});
</script>

<template>
  <CustomDrawer
    v-model:open="open" :title="title" placement="right" :width="630" destroy-on-close
    :footer-style="{ textAlign: 'right' }" @close="close"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 4 }" layout="vertical">
      <!-- <a-form-item label="账号" name="username">
        <a-input v-model:value="form.username" placeholder="请输入" autocomplete="off" />
      </a-form-item>
      <a-form-item label="密码" name="password">
        <a-input-password v-model:value="form.password" placeholder="请输入" autocomplete="new-password" />
      </a-form-item>
      <a-form-item label="确认密码" name="password_repeat">
        <a-input-password v-model:value="form.password_repeat" placeholder="请输入" autocomplete="new-password" />
      </a-form-item> -->
      <a-form-item label="姓名" name="name">
        <a-input v-model:value="form.name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="联系电话" name="phone">
        <a-input v-model:value="form.phone" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="所属厂家" name="company">
        <a-input v-model:value="form.company" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="角色" name="role">
        <a-select v-model:value="form.role">
          <a-select-option v-for="item in typeOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="Client_id" name="Client_id">
        <div class="text-[#2469F1] refresh flex items-center" @click="getClient">
          <img src="@/assets/images/data-center/api-data/refresh.png" class="w-16px h-16px mr-4px" alt=""> 刷新
        </div>
        <a-input v-model:value="form.clientId" placeholder="自动生成" disabled />
      </a-form-item>
      <a-form-item label="Client_secret" name="Client_secret">
        <a-input v-model:value="form.clientSecret" placeholder="自动生成" disabled />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="close">
        取消
      </a-button>
      <a-button type="primary" class="ml-2" :loading="loading" @click="onSubmit">
        确认
      </a-button>
    </template>
  </CustomDrawer>
</template>

<style lang="scss" scoped>
.refresh {
  position: absolute;
  top: -30px;
  right: 0;
  cursor: pointer;
}
</style>
