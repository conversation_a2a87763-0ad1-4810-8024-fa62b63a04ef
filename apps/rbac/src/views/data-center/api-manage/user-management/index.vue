<script setup lang="tsx">
import type { ProColumns } from '@pubinfo/pro-components';
import { ProTable } from '@pubinfo/pro-components';
import { message, Modal } from 'ant-design-vue';
import AddDrawer from './components/add-drawer.vue';
import ApiAuthorization from './components/api-authorization.vue';

const router = useRouter();

const tableRef = ref();
const addDrawer = ref();
const apiAuthorization = ref();

const typeOptions: Ref<{ label?: string, value?: string }[] | undefined> = ref([]);
function getRoleType() {
  getDictItemList({ dictCode: 'roleType' }).then((res: API.ResponseDataListDictItemVo) => {
    typeOptions.value = res.data?.map((e) => {
      return {
        label: e.dictLabel,
        value: e.dictValue,
      };
    });
  });
}
getRoleType();

const columns: ProColumns<any> = computed(() => {
  return [
    // {
    //   title: '账号',
    //   dataIndex: 'username',
    //   hideInSearch: true,
    //   width: 120,
    // },
    {
      title: '角色',
      dataIndex: 'role',
      valueType: 'a-select',
      fieldProps: {
        placeholder: '请选择角色',
        allowClear: true,
        options: typeOptions.value,
      },
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      hideInSearch: true,
      width: 100,
    },
    {
      hideInSearch: true,
      valueType: 'a-select',
      title: '所属厂家',
      dataIndex: 'company',
      fieldProps: {
        placeholder: '请选择所属厂家',
        allowClear: true,
        options: [],
      },
      width: 120,
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      width: 180,
    },
    {
      title: 'Client_id',
      dataIndex: 'clientId',
      hideInSearch: true,
      width: 180,
    },
    {
      title: 'Client_secret',
      dataIndex: 'clientSecret',
      hideInSearch: true,
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '设置超管',
      dataIndex: 'isAdmin',
      hideInSearch: true,
      width: 100,
    },
    {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      hideInSearch: true,
    },
  ];
});

// TODO
// 搜索项模型
interface SearchParams {
  text?: string
}

const searchParams = reactive<SearchParams>({
  text: '',
});

async function request(params: any) {
  const { current, pageSize, ...rest } = params;
  const res = await postApiUserGetUserMsg({
    name: searchParams.text,
    currentPage: current,
    pageSize,
    ...rest,
  });
  const tableData = res.data.content ?? [];
  const total = res.data.totalElements ?? 0;
  return {
    success: true,
    data: tableData,
    total,
  };
}

const isRecordValid = computed(() => (record) => {
  return record.status === 0;
});

// 刷新数据
function updateData() {
  tableRef.value.fetch();
}

function onAction(key: string, record: any) {
  switch (key) {
    case 'add':
      addDrawer.value.open('新增账号');
      break;
    case 'edit':
      addDrawer.value.open('编辑账号', record);
      break;
    case 'api':
      apiAuthorization.value.open(record);
      break;
    case 'del':
      if (!record.id) {
        message.error('无效的ID，无法删除');
        return;
      }
      Modal.confirm({
        title: '删除',
        content: '确定要删除该账号吗？',
        onOk: async () => {
          try {
            // 调用封装好的删除接口函数
            await postApiUserDeleteUser<Record<string, any>, Record<string, any>>({ userId: record.id.toString() });
            // 提示删除成功
            message.success('删除成功');
            // 刷新表格数据
            tableRef.value.fetch();
          }
          catch (error) {
            // 处理删除失败的情况
            message.error('删除失败，请稍后再试');
          }
        },
      });
      break;
    default:
      break;
  }
}

function handleIsAdminChange(record: any) {
  console.log(record);
}
</script>

<template>
  <div class="w-full h-full py-29px px-40px flex flex-col">
    <div
      class="bg-[url('@/assets/images/data-center/api-data/bg-user.png')] bg-no-repeat bg-right-top bg-[length:261.73px_254.14px]"
    >
      <div class="text-[24px] text-[#161E5D] font-semibold leading-[23px] mb-[40px]">
        账号管理
      </div>
      <div class="flex items-center mb-[40px] w-[80%]">
        <a-input v-model:value="searchParams.text" class="flex-1 mr-[16px]" size="large" placeholder="请输入姓名/厂家进行搜索">
          <template #prefix>
            <SearchOutlined />
          </template>
          <template #suffix>
            <a-button type="primary" @click="updateData">
              搜索
            </a-button>
          </template>
        </a-input>
        <a-button type="primary" class="ml-[16px]" @click="onAction('add')">
          <template #icon>
            <PlusOutlined />
          </template>
          新增用户
        </a-button>
      </div>
    </div>
    <div class=" bg-[#FFF] p-[30px]">
      <ProTable
        ref="tableRef" row-key="id" :request="request" :columns="columns" :toolbar="false"
        :scroll="{ x: 1100 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'role'">
            <div> {{ typeOptions.find(item => item.value === record.role)?.label }} </div>
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a-switch :checked="isRecordValid(record)" />
          </template>
          <template v-if="column.dataIndex === 'isAdmin'">
            <a-switch :checked="record.isAdmin" @change="handleIsAdminChange(record)" />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a class="flex items-center" @click="onAction('api', record)">
                <img class="w-14px h-14px" src="@/assets/images/data-center/api-data/auth.png" alt="">
                <span class="ml-4px">API授权</span>
              </a>
              <a class="flex items-center" @click="onAction('edit', record)">
                <EditOutlined />
                <span class="ml-4px">编辑</span>
              </a>
              <a class="flex items-center text-[#F05F5F]" @click="onAction('del', record)">
                <DeleteOutlined />
                <span class="ml-4px">删除</span>
              </a>
            </a-space>
          </template>
        </template>
      </ProTable>
    </div>
    <AddDrawer ref="addDrawer" @submit="updateData" />
    <ApiAuthorization ref="apiAuthorization" />
  </div>
</template>

<style lang="scss">
.isActive {
  color: #fff;
  background: #2469f1;
}
</style>
