<script setup lang="ts">
import type { FormExpose } from 'ant-design-vue/es/form/Form';
import type { TreeDataItem } from 'ant-design-vue/lib/tree-select/interface';
import { message } from 'ant-design-vue';
import { defineProps, ref } from 'vue';

// 表单数据模型
const props = defineProps({
  finalFormData: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['formDataChanged']);
const form = ref({
  code: '', // 编码
  name: '', // 接口名称
  keyWord: '', // 关键字
  fromSystem: '', // 来源系统
  summary: '', // 摘要
  // typeId: '', // API类型
  isValid: true, // 是否有效 0:有效 1:无效
  isView: true, // 是否展示 0:展示 1:不展示
});
watchEffect(() => {
  form.value = {
    code: props.finalFormData.code || '',
    name: props.finalFormData.name || '',
    keyWord: props.finalFormData.keyWord || '',
    fromSystem: props.finalFormData.fromSystem || '',
    summary: props.finalFormData.summary || '',
    // typeId: props.finalFormData.typeId || '',
    isValid: props.finalFormData.isValid === 0,
    isView: props.finalFormData.isView === 0,
  };
});

const pid = ref(0);
// API类型选项
const apiTypes = ref<TreeDataItem[]>([
  {
    title: '数据接口',
    value: '0',
    key: '0',
    isLeaf: false,
    children: [
      // {
      //   title: '子节点 1',
      //   value: '0-0',
      //   key: '0-0',
      //   isLeaf: false,
      //   children: [
      //     // {
      //     //   title: '子节点 1-1',
      //     //   value: '0-0-0',
      //     //   key: '0-0-0',
      //     //   isLeaf: true,
      //     // },
      //     // {
      //     //   title: '子节点 1-2',
      //     //   value: '0-0-1',
      //     //   key: '0-0-1',
      //     //   isLeaf: true,
      //     // },
      //   ],
      // },
      // {
      //   title: '子节点 2',
      //   value: '0-1',
      //   key: '0-1',
      //   isLeaf: true,
      // },
    ],
  },
]);

async function fetchSectionList(params) {
  try {
    const res = await postSortPage(params);
    if (res.code === 0) {
      apiTypes.value = res.data.map(item => ({
        label: item.name, // 显示的标签
        value: item.id, // 绑定的值
        key: item.id, // 唯一的key // 初始化子节点为空数组
        children: [], // 子节点数据数组
        isLeaf: false,
      }));
    }
  }
  catch (error) {
    console.error('Error fetching section list:', error);
  }
}
const formRef = ref<FormExpose>();
// 初始化加载第一层数据
const params = {
  currentPage: 1,
  pageSize: 999,
  pid: pid.value,
};
fetchSectionList(params);

// 异步加载子节点数据

// function loadData(treeNode) {
//   return new Promise((resolve, reject) => {
//     postSortSimplelist({ pid: treeNode.value })
//       .then((res) => {
//         if (res.code === 0) {
//           const children = res.data.map(item => ({
//             label: item.name,
//             value: item.id,
//             key: item.id,
//             isLeaf: res.data.length === 0,
//             children: [],
//           }));

//           // 查找父节点并将 children 作为其 children 属性添加
//           const parentIndex = apiTypes.value.findIndex(item => item.value === treeNode.value);
//           if (parentIndex !== -1) {
//             apiTypes.value[parentIndex].children = apiTypes.value[parentIndex].children.concat(children);
//           }

//           // 触发 a-tree-select 的更新
//           if (formRef.value) {
//             const treeSelect = formRef.value.$refs.typeId;
//             if (treeSelect) {
//               treeSelect.update();
//               // console.log('a-tree-select 组件已更新');
//             }
//           }

//           resolve(true);
//         }
//         else {
//           console.error('Failed to fetch children:', res.message);
//           resolve(false);
//         }
//       })
//       .catch((error) => {
//         console.error('Error fetching children:', error);
//         reject(error);
//       });
//   });
// }

// 表单引用

// 表单提交函数
async function submitForm() {
  if (formRef.value) {
    try {
      await formRef.value.validate();
      const formToSend = { ...form.value };
      formToSend.isValid = form.value.isValid ? 1 : 0;
      formToSend.isView = form.value.isView ? 1 : 0;
      emit('formDataChanged', formToSend);
      // message.success('表单提交成功');
      return true; // 验证成功
    }
    catch (error) {
      console.error('表单验证失败', error);
      message.error('表单验证失败');
      throw error; // 验证失败，抛出错误
    }
  }
  else {
    console.error('formRef.value is null or undefined');
    message.error('表单引用未初始化');
    throw new Error('FormRef is not initialized');
  }
}
defineExpose({
  submitForm,
});
</script>

<template>
  <a-form ref="formRef" :model="form" layout="vertical">
    <a-form-item
      label="编码"
      name="code"
      :rules="[{ required: true, message: '请输入编码', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.code" />
    </a-form-item>
    <a-form-item
      label="接口名称"
      name="name"
      :rules="[{ required: true, message: '请输入接口名称', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.name" />
    </a-form-item>
    <a-form-item
      label="关键字"
      name="keyWord"
      :rules="[{ required: true, message: '请输入关键字', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.keyWord" />
    </a-form-item>
    <a-form-item
      label="来源系统"
      name="fromSystem"
      :rules="[{ required: true, message: '请输入来源系统', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.fromSystem" />
    </a-form-item>
    <a-form-item
      label="摘要"
      name="summary"
      :rules="[{ required: true, message: '请输入摘要', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.summary" />
    </a-form-item>
    <!-- <a-form-item
      label="API类型"
      name="typeId"
      :rules="[{ required: true, message: '请选择API类型', trigger: 'change' }]"
    >
      <a-tree-select
        ref="typeId"
        v-model:value="form.typeId"
        :dropdown-match-select-width="false"
        :tree-data="apiTypes"
        placeholder="请选择API类型"
        style="width: 100%;"
        :load-data="loadData"
      />
    </a-form-item> -->
    <a-form-item label="是否启用 " name="isValid" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
      <a-radio-group v-model:value="form.isValid">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="是否展示" name="isView" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
      <a-radio-group v-model:value="form.isView">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <!-- <a-form-item>
      <a-button type="primary" @click="submitForm">提交</a-button>
    </a-form-item> -->
  </a-form>
</template>
