<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { defineProps, ref } from 'vue';
import { useDataSourceStore } from '@/store/dataSourceStore';

const props = defineProps({
  parameters: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['formDataChanged']);
const parameterNameOptions = ref([]);
const dataSourceStore = useDataSourceStore();
const formRef = ref<FormInstance | null>(null);
const form = ref({
  parameters: [
    {
      paraName: '', // 参数名称
      paraType: '', // 参数类型
      paraDesc: '', // 参数描述
      isEmpty: false, // 是否为空0:为空1:不为空
    },
  ],
});
watchEffect(() => {
  if (props.parameters.length === 0) {
    form.value.parameters = [
      {
        paraName: '', // 参数名称
        paraType: '', // 参数类型
        paraDesc: '', // 参数描述
        isEmpty: false, // 是否为空 false:为空 true:不为空
      },
    ];
  }
  else {
    // 否则，根据props.parameters进行映射
    form.value.parameters = props.parameters.map(param => ({
      ...param,
      isEmpty: param.isEmpty === 1, // 将isEmpty的值转换为布尔类型
    }));
  }
});
// const parameterTypeOptions = ref([]);

// 添加表单项
function addParameter() {
  form.value.parameters.push({
    paraName: '',
    paraType: '',
    paraDesc: '',
    isEmpty: false,
  });
}

// 删除表单项
function removeParameter(index: number) {
  form.value.parameters.splice(index, 1);
}

function handleParameterNameChange(param: any) {
  const paraType = parameterNameOptions.value.find(item => item.value === param.paraName)?.paraType;
  param.paraType = paraType;
}

async function submitForm() {
  if (formRef.value) {
    try {
      await formRef.value.validate();
      const formToSend = { ...form.value };
      formToSend.parameters = formToSend.parameters.map(param => ({
        ...param,
        isEmpty: param.isEmpty ? 1 : 0,
      }));
      emit('formDataChanged', formToSend);
      // message.success('表单提交成功');
      return true;
    }
    catch (validationError) {
      console.error('表单验证失败:', validationError);
      message.error('表单验证失败，请检查输入');
      throw error;
    }
  }
}

function handleDataWarehouseModelColumns() {
  getApiDataGetApiInOut({ tableId: dataSourceStore.dataSourceId }).then((res) => {
    if (res.code === 0) {
      //  console.log('获取数据仓库模型列信息成功，响应数据:', res);
      parameterNameOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.name,
        paraType: item.dataType,
      }));
      // parameterTypeOptions.value = res.data.map(item => ({
      //   label: item.dataType,
      //   value: item.dataType,
      // }));
    }

    else {
      console.error('获取数据仓库模型列信息失败，响应代码:', res.code);
    }
  }).catch((error) => {
    console.error('获取数据仓库模型列信息失败：', error);
  });
}

watch(() => dataSourceStore.dataSourceId, (newVal) => {
  if (newVal) {
    handleDataWarehouseModelColumns();
  }
}, { immediate: true });
defineExpose({
  submitForm,
});
</script>

<template>
  <div>
    <div class="flex items-center box">
      <img src="@/assets/images/data-center/api-data/step4-icon.png">
      <div class="ml-10px basic-title">
        出参数据
      </div>
      <div class="ml-auto">
        <a-button type="primary" ghost @click="addParameter">
          一键生成
        </a-button>
        <a-button class="ml-10px" danger @click="addParameter">
          全部清空
        </a-button>
      </div>
    </div>
    <a-form ref="formRef" :model="form" layout="vertical">
      <div v-for="(param, index) in form.parameters" :key="index">
        <div class="top flex justify-between">
          <div class="top-box">
            出参{{ index + 0 }}
          </div>
          <div class="button-box">
            <img class="mr-[-12px]" src="@/assets/images/data-center/api-data/delete.png">
            <a-button type="link" danger @click="removeParameter(index)">
              删除
            </a-button>
          </div>
        </div>
        <div class="form-container">
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="参数名称" :name="['parameters', index, 'paraName']" :rules="[{ required: true, message: '请选择参数名称', trigger: 'change' }]">
                <a-select v-model:value="param.paraName" @change="handleParameterNameChange(param)">
                  <a-select-option v-for="item in parameterNameOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="参数类型" :name="['parameters', index, 'paraType']" :rules="[{ required: true, message: '请选择参数类型', trigger: 'change' }]">
                <!-- <a-select v-model:value="param.paraType">
                  <a-select-option v-for="item in parameterTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select> -->
                <a-input v-model:value="param.paraType" disabled />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="参数描述" :name="['parameters', index, 'paraDesc']" :rules="[{ required: true, message: '请输入参数描述', trigger: 'blur' }]">
                <a-input v-model:value="param.paraDesc" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否为空" :name="['parameters', index, 'isEmpty']" :rules="[{ required: true, message: '请选择是否为空', trigger: 'change' }]">
                <a-radio-group v-model:value="param.isEmpty">
                  <a-radio :value="true">
                    是
                  </a-radio>
                  <a-radio :value="false">
                    否
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
      <a-form-item>
        <a-button class="mt-[20px]" type="primary" ghost @click="addParameter">
          + 新增出参
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="scss">
.top-box {
  width: 94px;
  height: 34px;
  padding: 6px 0 0;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  background: linear-gradient(270deg, #f5ab1a 0%, #fcc250 100%);
  border-radius: 10px 10px 0 0;
}

.button-box {
  display: flex;
  align-items: center;
}

.form-container {
  padding: 20px 30px;
  background: #fff9ee;
}

.basic-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 21px;
  color: #161e5d;
}

.box {
  padding-bottom: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}
</style>
