<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { defineProps, nextTick, ref, watch } from 'vue';
import { useDataSourceStore } from '@/store/dataSourceStore';

const props = defineProps({
  type: {
    type: String,
    default: 'api',
  },
  apiNode: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(['formDataChanged']);
const dataSourceStore = useDataSourceStore();
const isInitializing = ref(false); // 添加标志位
const form = ref({
  name: '', // 节点名称
  projectId: '', // 数据板块
  domainId: [], // 主题域（改为数组，支持级联选择）
  tableId: '', // 数据表
  version: '', // 版本号
  returnType: '', // 返回类型
  url: '', // 节点接口地址
  isOrder: false, // 是否排序
  returnIsEncr: false, // 返回数据是否加密
  isPage: false, // 返回结果是否分页
  type: false, // 节点接口类型 0:读 1:写
  isDistinct: false, // 是否去重
  requestMethod: undefined, // 代理方法
  uri: undefined, // 代理路IP+PORT
  uriToPath: undefined, // 代理路URI
});

const projectIdOptions = ref([]);
const themeDomains = ref([]); // 主题域树形选项
const tableIdOptions = ref([]);
const versionOptions = ref([{ label: '1.0.0', value: '1.0.0' }]);
const returnTypeOptions = ref([{ label: 'JSON', value: 'json' }]);
const formRef = ref<FormInstance | null>(null);

// 监听 props.apiNode 初始化表单
watch(
  () => props.apiNode,
  async (newApiNode) => {
    isInitializing.value = true;
    const targetDomainId = newApiNode.domainId ? String(newApiNode.domainId) : '';

    // 初始赋值
    form.value = {
      name: newApiNode.name || '',
      domainId: targetDomainId ? findPathById(themeDomains.value, targetDomainId)?.map(node => node.value) || [] : [],
      tableId: newApiNode.tableId || '',
      projectId: newApiNode.projectId ? String(newApiNode.projectId) : '',
      version: newApiNode.version || '',
      returnType: newApiNode.returnType || '',
      url: newApiNode.url || '',
      isOrder: newApiNode.isOrder === 1,
      returnIsEncr: newApiNode.returnIsEncr === 1,
      isPage: newApiNode.isPage === 1,
      type: newApiNode.type === 1,
      isDistinct: newApiNode.isDistinct === 1,

      requestMethod: newApiNode.requestMethod || '',
      uri: newApiNode.uri || '',
      uriToPath: newApiNode.uriToPath || '',
    };
    // console.log('Initial tableId:', form.value.tableId);

    // 等待异步操作完成
    if (newApiNode.projectId) {
      await handleProject();
      await handleDomain(); // 确保 handleDomain 完成
    }
    if (newApiNode.tableId) {
      await handleTable();
    }
    await nextTick();
    isInitializing.value = false;
    // console.log('Initialization complete, tableId:', form.value.tableId);
  },
  { immediate: true, deep: true },
);

// 根据 value 查找级联路径
function findPathById(tree, targetVal) {
  function dfs(node, path) {
    path.push(node);
    if (node.value === targetVal) {
      return path;
    }
    if (node.children) {
      for (const child of node.children) {
        const result = dfs(child, path);
        if (result) {
          return result;
        }
      }
    }
    path.pop();
    return null;
  }
  for (const root of tree) {
    const result = dfs(root, []);
    if (result) {
      return result;
    }
  }
  return null;
}

// 获取数据板块
async function handleProject() {
  const res = await postDataArchitectureDataProjectPageQuery({
    currentPage: 1,
    pageSize: 999,
    doSearchTotal: true,
  });
  if (res.code === 0) {
    projectIdOptions.value = res.data.records.map(item => ({
      label: item.zhName,
      value: String(item.id),
    }));
  }
}
handleProject();

// 获取主题域（树形结构）
async function handleDomain() {
  if (!form.value.projectId) {
    return;
  }
  const res = await getDataArchitectureDataProjectDomainQueryTree({
    projectid: form.value.projectId,
  });
  if (res.code === 0) {
    themeDomains.value = convertToCascaderOptions(res.data);
    const targetDomainId = props.apiNode.domainId ? String(props.apiNode.domainId) : '';
    if (targetDomainId && isInitializing.value) { // 仅在初始化时更新
      const path = findPathById(themeDomains.value, targetDomainId);
      form.value.domainId = path ? path.map(node => node.value) : [];
      // console.log('domainId updated by handleDomain:', form.value.domainId);
    }
  }
}

// 将树形数据转换为级联选择器格式
function convertToCascaderOptions(data) {
  return data.map(item => ({
    value: String(item.id),
    label: item.name,
    children: item.children ? convertToCascaderOptions(item.children) : null,
  }));
}

// 获取数据表
async function handleTable() {
  if (!form.value.projectId || !form.value.domainId.length) {
    return;
  }
  const res = await getApiDataGetTableMsg({
    projectId: form.value.projectId,
    domainId: form.value.domainId.slice(-1)[0],
  });
  if (res.code === 0) {
    tableIdOptions.value = res.data.map(item => ({
      label: item.tableName,
      value: item.id,
    }));
  }
}

// 监听 projectId 变化
watch(
  () => form.value.projectId,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      form.value.domainId = [];
      if (!isInitializing.value) {
        form.value.tableId = '';
        // console.log('tableId cleared by projectId change:', form.value.tableId);
      }
      handleDomain();
    }
  },
);

// 监听 domainId 变化
watch(
  () => form.value.domainId,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal.length > 0) {
      // console.log('domainId changed:', newVal);
      if (!isInitializing.value) {
        form.value.tableId = '';
        // console.log('3. tableId cleared by domainId change:', form.value.tableId);
      }
      handleTable();
    }
  },
  { deep: true },
);

// 监听 tableId 变化，更新 Pinia store
watch(() => form.value.tableId, (newVal, oldVal) => {
  // console.log('tableId changed:', newVal);
  if (newVal !== oldVal) {
    dataSourceStore.setDataSourceId(newVal);
  }
});

// 表单提交
async function submitForm() {
  if (formRef.value) {
    try {
      await formRef.value.validate();
      const formToSend = {
        ...form.value,
        domainId: form.value.domainId.length > 0 ? form.value.domainId.slice(-1)[0] : '',
        isOrder: form.value.isOrder ? 1 : 0,
        returnIsEncr: form.value.returnIsEncr ? 1 : 0,
        isPage: form.value.isPage ? 1 : 0,
        isDistinct: form.value.isDistinct ? 1 : 0,
        type: form.value.type ? 1 : 0,
      };
      emit('formDataChanged', formToSend);
      // message.success('表单提交成功');
      return true;
    }
    catch (error) {
      console.error('表单验证失败:', error);
      message.error('表单验证失败，请检查输入');
      throw error;
    }
  }
  else {
    console.error('表单引用未初始化');
    message.error('表单引用未初始化');
    throw new Error('表单引用未初始化');
  }
}

defineExpose({ submitForm });
</script>

<template>
  <a-form ref="formRef" :model="form" layout="vertical">
    <a-form-item label="名称" name="name" :rules="[{ required: true, message: '请输入名称', trigger: 'blur' }]">
      <a-input v-model:value="form.name" />
    </a-form-item>
    <a-form-item
      label="数据板块"
      name="projectId"
      :rules="[{ required: true, message: '请选择数据板块', trigger: 'change' }]"
    >
      <a-select v-model:value="form.projectId">
        <a-select-option v-for="item in projectIdOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      label="主题域"
      name="domainId"
      :rules="[{ required: true, message: '请选择主题域', trigger: 'change' }]"
    >
      <a-cascader
        v-model:value="form.domainId"
        :options="themeDomains"
        placeholder="请先选择数据板块"
        change-on-select
      />
    </a-form-item>
    <a-form-item
      label="数据表"
      name="tableId"
      :rules="[{ required: true, message: '请选择数据表', trigger: 'change' }]"
    >
      <a-select v-model:value="form.tableId">
        <a-select-option v-for="item in tableIdOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      label="版本号"
      name="version"
      :rules="[{ required: true, message: '请选择版本号', trigger: 'change' }]"
    >
      <a-select v-model:value="form.version">
        <a-select-option v-for="item in versionOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      label="返回类型"
      name="returnType"
      :rules="[{ required: true, message: '请选择返回类型', trigger: 'change' }]"
    >
      <a-select v-model:value="form.returnType">
        <a-select-option v-for="item in returnTypeOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="props.type === 'proxy'"
      label="代理方法"
      name="requestMethod"
      :rules="[{ required: true, message: '请选择代理方法', trigger: 'change' }]"
    >
      <a-select v-model:value="form.requestMethod">
        <a-select-option value="POST">
          POST
        </a-select-option>
        <a-select-option value="GET">
          GET
        </a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="props.type === 'proxy'"
      label="代理路IP+PORT"
      name="uri"
      placeholder="请输入http(s)://IP:PORT的格式"
      :rules="[{ required: true, message: '请输入代理路IP+PORT', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.uri" />
    </a-form-item>
    <a-form-item
      v-if="props.type === 'proxy'"
      label="代理路URI"
      name="uriToPath"
      placeholder="请输入代理路径"
      :rules="[{ required: true, message: '请输入代理路径', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.uriToPath" />
    </a-form-item>
    <a-form-item
      label="节点接口地址"
      name="url"
      placeholder="节点接口地址必须不一样"
      :rules="[{ required: true, message: '请输入节点接口地址', trigger: 'blur' }]"
    >
      <a-input v-model:value="form.url" />
    </a-form-item>
    <a-form-item label="是否执行排序" name="isOrder" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
      <a-radio-group v-model:value="form.isOrder">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      label="返回数据是否加密"
      name="returnIsEncr"
      :rules="[{ required: true, message: '请选择', trigger: 'blur' }]"
    >
      <a-radio-group v-model:value="form.returnIsEncr">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="是否分页" name="isPage" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
      <a-radio-group v-model:value="form.isPage">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="节点类型" name="type" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
      <a-radio-group v-model:value="form.type">
        <a-radio :value="false">
          读
        </a-radio>
        <a-radio :value="true">
          写
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="是否去重" name="isDistinct" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
      <a-radio-group v-model:value="form.isDistinct">
        <a-radio :value="true">
          是
        </a-radio>
        <a-radio :value="false">
          否
        </a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>
