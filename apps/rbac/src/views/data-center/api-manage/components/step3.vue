<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { defineProps, ref } from 'vue';
import { useDataSourceStore } from '@/store/dataSourceStore';

const props = defineProps({
  parameters: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(['formDataChanged']);
const parameterNameOptions = ref([]);
const dataSourceStore = useDataSourceStore();
// console.log('props:', props.parameters);
const formRef = ref<FormInstance | null>(null);
const form = ref({
  parameters: [
    {
      paraName: '', // 参数名称
      paraType: '', // 参数类型
      paraAlias: '', // 参数别名
      paraDesc: '', // 参数描述
      paraValue: '', // 参数值
      isMandatory: false, // 是否必选 0：不必选 1：必选
      isHide: false, // 是否隐藏 0：不隐藏 1：隐藏
      isIn: false, // 是否可以多参数传值0：不可，1：可以
      paraRule: '', // 参数规则 条件比较
      inValue: '', // 条件值
      splitChar: ',', // 默认多参数传值分隔符
    },
  ],
});
watchEffect(() => {
  if (props.parameters.length === 0) {
    form.value.parameters = [
      {
        paraName: '', // 参数名称
        paraType: '', // 参数类型
        paraAlias: '', // 参数别名
        paraDesc: '', // 参数描述
        paraValue: '', // 参数值
        isMandatory: false, // 是否必选 0：不必选 1：必选
        isHide: false, // 是否隐藏 0：不隐藏 1：隐藏
        isIn: false, // 是否可以多参数传值0：不可，1：可以
        paraRule: '', // 参数规则 条件比较
        inValue: '', // 条件值
        splitChar: ',', // 默认多参数传值分隔符
      },
    ];
  }
  else {
    // 否则，根据props.parameters进行映射
    form.value.parameters = props.parameters.map(param => ({
      ...param,
      isMandatory: param.isMandatory === 1,
      isHide: param.isHide === 1,
      isIn: param.isIn === 1,
    }));
  }
});

// const parameterTypeOptions = ref([
//   { label: 'String', value: 'String' },
// ]);

const conditionOptions = ref([
  { label: '=', value: '=' },
  { label: '>', value: '>' },
  { label: '>=', value: '>=' },
  { label: '<=', value: '<=' },
  { label: '<', value: '<' },
  { label: 'in', value: 'in' },
  { label: 'like', value: 'like' },
]);

function addParameter() {
  form.value.parameters.push({
    paraName: '',
    paraType: '',
    paraAlias: '',
    paraDesc: '',
    paraValue: '',
    isMandatory: false,
    isHide: false,
    isIn: false,
    paraRule: '',
    inValue: '',
    splitChar: ':', // 初始化为空字符串
  });
}
function handleDataWarehouseModelColumns() {
  getApiDataGetApiInOut({ tableId: dataSourceStore.dataSourceId }).then((res) => {
    if (res.code === 0) {
      //  console.log('获取数据仓库模型列信息成功，响应数据:', res);
      parameterNameOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.name,
        paraType: item.dataType,
      }));
      // parameterTypeOptions.value = res.data.map(item => ({
      //   label: item.dataType,
      //   value: item.dataType,
      // }));
    }

    else {
      console.error('获取数据仓库模型列信息失败，响应代码:', res.code);
    }
  }).catch((error) => {
    console.error('获取数据仓库模型列信息失败：', error);
  });
}

watch(() => dataSourceStore.dataSourceId, (newVal) => {
  // console.log('newVal:', newVal);
  if (newVal) {
    handleDataWarehouseModelColumns();
  }
}, { immediate: true });

function removeParameter(index: number) {
  form.value.parameters.splice(index, 1);
}

function handleParameterNameChange(param: any) {
  const paraType = parameterNameOptions.value.find(item => item.value === param.paraName)?.paraType;
  param.paraType = paraType;
}

async function submitForm() {
  if (formRef.value) {
    // console.log('formToSend:', form.value);
    try {
      await formRef.value.validate();
      const formToSend = { ...form.value };
      formToSend.parameters = formToSend.parameters.map(param => ({
        ...param,
        isMandatory: param.isMandatory ? 1 : 0,
        isHide: param.isHide ? 1 : 0,
        isIn: param.isIn ? 1 : 0,
      }));
      emit('formDataChanged', formToSend);
      // message.success('表单提交成功');
      return true;
    }
    catch (validationError) {
      console.error('表单验证失败:', validationError);
      message.error('表单验证失败，请检查输入');
      throw error;
    }
  }
}

defineExpose({
  submitForm,
});
</script>

<template>
  <div>
    <a-form ref="formRef" :model="form" layout="vertical">
      <div v-for="(param, index) in form.parameters" :key="index">
        <div class="top flex justify-between">
          <div class="top-box">
            入参{{ index + 1 }}
          </div>
          <div class="button-box">
            <img class="mr-[-12px]" src="@/assets/images/data-center/api-data/delete.png">
            <a-button type="link" danger @click="removeParameter(index)">
              删除
            </a-button>
          </div>
        </div>
        <div class="form-container">
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="参数名称" :name="['parameters', index, 'paraName']" :rules="[{ required: true, message: '请选择参数名称', trigger: 'change' }]">
                <a-select v-model:value="param.paraName" @change="handleParameterNameChange(param)">
                  <a-select-option v-for="item in parameterNameOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="参数类型" :name="['parameters', index, 'paraType']" :rules="[{ required: true, message: '请选择参数类型', trigger: 'change' }]">
                <a-input v-model:value="param.paraType" :disabled="true" />
                <!-- <a-select v-model:value="param.paraType">
                  <a-select-option v-for="item in parameterTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select> -->
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="参数别名" :name="['parameters', index, 'paraAlias']">
                <a-input v-model:value="param.paraAlias" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="参数值" :name="['parameters', index, 'paraValue']">
                <a-input v-model:value="param.paraValue" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="参数描述" :name="['parameters', index, 'paraDesc']">
                <a-input v-model:value="param.paraDesc" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="条件比较" :name="['parameters', index, 'paraRule']">
                <a-select v-model:value="param.paraRule">
                  <a-select-option v-for="item in conditionOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6" class="mt-[6px]">
              <a-form-item label="条件值" :name="['parameters', index, 'inValue']">
                <a-input v-model:value="param.inValue" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row gutter="16" type="flex" align="middle">
            <a-col :span="12">
              <a-form-item label="是否必选" :name="['parameters', index, 'isMandatory']" :rules="[{ required: true, message: '请输入条件值', trigger: 'blur' }]">
                <a-radio-group v-model:value="param.isMandatory">
                  <a-radio :value="true">
                    是
                  </a-radio>
                  <a-radio :value="false">
                    否
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否隐藏" :name="['parameters', index, 'isHide']" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
                <a-radio-group v-model:value="param.isHide">
                  <a-radio :value="true">
                    是
                  </a-radio>
                  <a-radio :value="false">
                    否
                  </a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="是否允许多值输入" :name="['parameters', index, 'isIn']" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
            <a-radio-group v-model:value="param.isIn">
              <a-radio :value="true">
                是
              </a-radio>
              <a-radio :value="false">
                否
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <div :name="`parameters[${index}].splitChar`">
            <a-input v-model:value="param.splitChar" placeholder="请输入" :disabled="!param.isIn" />
          </div>
        </div>
      </div>
      <a-form-item>
        <a-button class="mt-[20px]" type="primary" ghost @click="addParameter">
          + 新增入参
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="scss">
.top-box {
  width: 94px;
  height: 34px;
  padding: 6px 0 0;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  text-align: center;
  background: linear-gradient(270deg, #346dea 0%, #5b8cf7 100%);
  border-radius: 10px 10px 0 0;
}

.button-box {
  display: flex;
  align-items: center;
}

.form-container {
  padding: 20px 30px;
  background: #f8f9ff;
}
</style>
