<script setup lang="tsx">
import type { ProColumns } from '@pubinfo/pro-components';
import { ProTable } from '@pubinfo/pro-components';
import { Badge, message, Modal } from 'ant-design-vue';
import EntityDialog from '../components/businessDialog.vue';
import ViewInstanceResult from '../components/viewInstanceResult.vue';
import { ACTION } from '../enum';
import RelationshipDrawer from './relationship-drawer.vue';
import TableDrawer from './table-drawer.vue';

const emit = defineEmits(['openPage']);

const router = useRouter();
const route = useRoute();

const tableRef = ref();

// TODO
interface mockAPI {
  /** 实例ID */
  id?: string
  /** 任务名称 */
  taskName?: string
  /** 数据源类型 */
  dataSrcType?: string
  /** 数据源名称 */
  dataSrcName?: string
  /** 采集类型 */
  collectType?: string
  /** 采集时间 */
  collectTime?: string
  /** 运行时长 */
  runTime?: string
  /** 状态 */
  status?: string
  /** 负责人 */
  chargeMan?: string
}

async function request(params: any) {
  const { current, pageSize, ...rest } = params;
  const res = await postDataArchitectureDataProjectDomainEntityPageQuery({
    currentPage: current,
    pageSize,
    ...rest,
    projectid: route.query.id,
  });
  return {
    success: res.success,
    data: res.data?.records.map((item) => {
      return {
        ...item,
        children: [],
      };
    }) ?? [],
    total: res.data?.total ?? 0,
  };
}
const columns: ProColumns<mockAPI> = [
  {
    width: 200,
    title: '实体名称/编码',
    dataIndex: 'name',
    hideInSearch: true,
    fixed: 'left',
  },
  {
    width: 100,
    title: '数据板块',
    dataIndex: 'projectName',
    hideInSearch: true,
  },
  {
    width: 150,
    title: '主题域',
    hideInSearch: true,
    dataIndex: 'domainName',
  },
  {
    width: 100,
    title: '实体类型',
    hideInSearch: true,
    dataIndex: 'type',
  },
  {
    width: 100,
    title: '状态',
    hideInSearch: true,
    dataIndex: 'isonline',
    customRender: ({ text }: { text: number }) => {
      return (
        text
          ? <Badge status="success" text="上线" />
          : <Badge status="error" text="下线" />
      );
    },
  },
  {
    width: 100,
    title: '相关逻辑表',
    hideInSearch: true,
    dataIndex: 'counttable',
  },

  {
    width: 80,
    title: '负责人',
    hideInSearch: true,
    dataIndex: 'ownerName',
  },
  {
    width: 450,
    title: '操作',
    hideInSearch: true,
    dataIndex: 'action',
    fixed: 'right',
  },
];

const expandedRowKeys = ref([]);
function expand(expanded: any, record: any) {
  if (expanded) {
    const params = {
      currentPage: 1,
      pageSize: 999,
      projectid: route.query.id,
      pid: record.id,
    };
    postDataArchitectureDataProjectDomainEntityPageQuery(params).then((res: any) => {
      record.children = res.data.records.map((item: any) => {
        return {
          ...item,
          children: [],
        };
      });
      expandedRowKeys.value.push(record.id);
    });
  }
  else {
    expandedRowKeys.value.splice(expandedRowKeys.value.indexOf(record.id), 1);
  }
}

// 查看版本
const viewResultRef = ref();
function openVersionDrawer(record) {
  viewResultRef.value.open(record);
}

// 新增实体/编辑实体
const EntityDialogRef = ref();
const currentRecord = ref();
function addEntity(record) {
  currentRecord.value = record;
  EntityDialogRef.value.open(record);
}

const TableDrawerRef = ref();
const RelationshipDrawerRef = ref();
function handleTable(record) {
  TableDrawerRef.value.open(record);
}

// 刷新数据
function updateData(id) {
  if (!id || id === 0) {
    tableRef.value.fetch();
  }
  else {
    const params = {
      currentPage: 1,
      pageSize: 999,
      projectid: route.query.id,
      pid: id,
    };
    postDataArchitectureDataProjectDomainEntityPageQuery(params).then((res: any) => {
      findNode(tableRef.value.dataSource, id).children = res.data.records;
    });
  }
}

// 递归查询树结构中的某个节点
function findNode(tree, nodeId) {
  for (const node of tree) {
    if (node.id === nodeId) {
      return node;
    }
    if (node.children) {
      const result = findNode(node.children, nodeId);
      if (result) {
        return result;
      }
    }
  }
  return null;
}
// 递归查询树结构中的某个节点的父节点
function findNodeFather(tree, nodeId) {
  for (const node of tree) {
    if (node.id === nodeId) {
      return 0;
    }
    if (node.children) {
      const result = findNode(node.children, nodeId);
      if (result) {
        return node.id;
      }
    }
  }
  return null;
}

function handleChangeStatus(record) {
  Modal.confirm({
    title: '删除',
    content: `确定要${record.isonline === 0 ? '上线' : '下线'}该业务实体吗？`,
    onOk() {
      postDataArchitectureDataProjectDomainEntityChange({ id: record?.id as number }).then((res) => {
        if (res.success) {
          record.isonline = record.isonline === 0 ? 1 : 0;
          message.success('成功');
        }
      });
    },
  });
}

function handleDel(id: string | number) {
  Modal.confirm({
    title: '删除确认',
    icon: () => (<ExclamationCircleFilled style="color: #F05F5F" />),
    content: '是否确认删除该业务实体?',
    onOk() {
      postDataArchitectureDataProjectDomainEntityDeleted({ id: id as number }).then((res) => {
        if (res.success) {
          message.success('删除成功');
          updateData(findNodeFather(tableRef.value.dataSource, id));
        }
      });
    },
    onCancel() {
      // console.log('Cancel');
    },
  });
}

function handleViewRelation(record) {
  RelationshipDrawerRef.value.open(record);
}

function openLogPage() {
  emit('openPage', 'log');
}

defineExpose({
  addEntity,
});
</script>

<template>
  <div>
    <ProTable
      ref="tableRef" row-key="id" :request="request" :columns="columns" :toolbar="false" :search="false"
      bordered :scroll="{ x: 1200 }" :expanded-row-keys="expandedRowKeys" @expand="expand"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          {{ record.name }}/{{ record.code }}
        </template>
        <template v-if="column.dataIndex === 'counttable'">
          <div class="flex items-center">
            {{ record.counttable }}
            <img src="@/assets/images/data-center/api-data/table.png" class="w-16px h-16px ml-4px cursor-pointer" alt="" @click="handleTable(record)">
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a-button type="link" @click="handleChangeStatus(record)">
              <RightSquareOutlined class="mr-4px" />{{ record.isonline === 0 ? '上线' : '下线' }}
            </a-button>
            <a-button type="link" @click="handleViewRelation(record)">
              <EyeOutlined />查看关联关系
            </a-button>
            <a-button type="link" :disabled="record.isonline === 1" @click="addEntity(record);">
              <EditOutlined class="mr-4px" />{{ ACTION.EDIT }}
            </a-button>
            <a-dropdown>
              <a-button type="link" class="flex items-center ">
                <MoreOutlined />
                <span class="ml-4px">更多</span>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-button type="link" class="flex items-center" @click="openVersionDrawer(record)">
                      <PubSvgIcon name="history" />
                      <span class="ml-4px text-[#2469F1]">查看版本</span>
                    </a-button>
                  </a-menu-item>
                  <a-menu-item>
                    <a-button type="link" class="flex items-center" @click="handleDel(record.id)">
                      <PubSvgIcon name="delete" class="text-[#F05F5F]" />
                      <span class="ml-4px text-[#F05F5F]">{{ ACTION.REMOVE }}</span>
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </ProTable>
    <ViewInstanceResult ref="viewResultRef" />
    <EntityDialog ref="EntityDialogRef" @submit="updateData" />
    <TableDrawer ref="TableDrawerRef" />
    <RelationshipDrawer ref="RelationshipDrawerRef" />
  </div>
</template>

<!-- <style lang="scss" scoped>
.container {
  width: 100%;
  height: 500px;
}
</style> -->
