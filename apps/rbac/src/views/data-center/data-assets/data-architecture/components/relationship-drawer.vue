<script setup lang="tsx">
import LogicFlow from '@logicflow/core';
import { useToggle } from '@vueuse/core';
import CustomDrawer from '@/components/CustomDrawer/index.vue';
import '@logicflow/core/lib/style/index.css';

defineOptions({
  name: 'RelationshipDrawer',
});
// const emit = defineEmits(['submit']);
const [open, setOpen] = useToggle(false);
const lf = ref<LogicFlow>();
const containerRef = ref<HTMLElement | null>(null);
const initDrawData = ref<any>({});

function initFlowInstance() {
  if (containerRef.value) {
    lf.value = new LogicFlow({
      container: containerRef.value,
      background: {
        backgroundColor: '#F8F9FF',
      },
      grid: true,
      nodeTextEdit: false, // 节点是否可编辑。false不可编辑
      edgeTextEdit: false, // 边是否可编辑。false不可编辑
    });

    // 渲染图
    lf.value.render(initDrawData.value);
  }
}

async function onOpen(record: any) {
  setOpen(true);

  const res = await getDataArchitectureDataProjectDomainEntityGetRelation({
    Tableid: record.id,
  });

  initDrawData.value = {
    nodes: [
      {
        id: '1',
        type: 'rect',
        x: 100,
        y: 100,
        text: '节点1',
      },
      {
        id: '2',
        type: 'rect',
        x: 200,
        y: 200,
        text: '节点2',
      },
      {
        id: '3',
        type: 'rect',
        x: 300,
        y: 300,
        text: '节点3',
      },
    ],
    edges: [
      {
        id: '1-2',
        type: 'polyline',
        sourceNodeId: '1',
        targetNodeId: '2',
      },
      {
        id: '2-3',
        type: 'polyline',
        sourceNodeId: '2',
        targetNodeId: '3',
      },
    ],
  };

  console.log(res, 'res');

  initFlowInstance();
}

function close() {
  setOpen(false);
}

defineExpose({
  open: onOpen,
});

onMounted(() => {

});
</script>

<template>
  <CustomDrawer
    v-model:open="open"
    title="关联关系"
    placement="right"
    :width="800"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }" @close="close"
  >
    <div ref="containerRef" class="h-full w-full" />
  </CustomDrawer>
</template>
