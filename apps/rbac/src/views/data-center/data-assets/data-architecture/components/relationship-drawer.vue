<script setup lang="tsx">
import LogicFlow from '@logicflow/core';
import { useToggle } from '@vueuse/core';
import CustomDrawer from '@/components/CustomDrawer/index.vue';
import '@logicflow/core/lib/style/index.css';

defineOptions({
  name: 'RelationshipDrawer',
});
// const emit = defineEmits(['submit']);
const [open, setOpen] = useToggle(false);
const lf = ref<LogicFlow>();
const containerRef = ref<HTMLElement | null>(null);
const initDrawData = ref<any>({});

// 将树形结构拍平的方法
function flattenTreeData(treeData: any[]): any[] {
  const result: any[] = [];

  function traverse(nodes: any[]) {
    if (!Array.isArray(nodes)) {
      return;
    }

    nodes.forEach((node) => {
      const { children, ...nodeData } = node;
      result.push(nodeData);

      if (children && Array.isArray(children) && children.length > 0) {
        traverse(children);
      }
    });
  }

  traverse(treeData);
  return result;
}

// 将拍平后的关系数据转换为LogicFlow格式
function convertToLogicFlowData(flattenedData: any[]) {
  const nodes: any[] = [];
  const edges: any[] = [];
  const nodeMap = new Map(); // 用于去重和记录节点位置

  let nodeIndex = 0;
  const nodeSpacing = 200; // 节点间距
  const nodesPerRow = 3; // 每行节点数

  flattenedData.forEach((item, index) => {
    const { fromTableName, fromTableid, toTableName, toTableid, relation, attribute } = item;

    // 添加源节点
    if (!nodeMap.has(fromTableid)) {
      const row = Math.floor(nodeIndex / nodesPerRow);
      const col = nodeIndex % nodesPerRow;

      nodes.push({
        id: `node_${fromTableid}`,
        type: 'rect',
        x: 100 + col * nodeSpacing,
        y: 100 + row * nodeSpacing,
        text: fromTableName,
        properties: {
          tableId: fromTableid,
          tableName: fromTableName,
        },
      });
      nodeMap.set(fromTableid, nodeIndex);
      nodeIndex++;
    }

    // 添加目标节点
    if (!nodeMap.has(toTableid)) {
      const row = Math.floor(nodeIndex / nodesPerRow);
      const col = nodeIndex % nodesPerRow;

      nodes.push({
        id: `node_${toTableid}`,
        type: 'rect',
        x: 100 + col * nodeSpacing,
        y: 100 + row * nodeSpacing,
        text: toTableName,
        properties: {
          tableId: toTableid,
          tableName: toTableName,
        },
      });
      nodeMap.set(toTableid, nodeIndex);
      nodeIndex++;
    }

    // 添加边
    edges.push({
      id: `edge_${index}`,
      type: 'polyline',
      sourceNodeId: `node_${fromTableid}`,
      targetNodeId: `node_${toTableid}`,
      text: attribute,
      properties: {
        relation,
        attribute,
        fromTableName,
        toTableName,
      },
    });
  });

  return { nodes, edges };
}

function initFlowInstance() {
  if (containerRef.value) {
    lf.value = new LogicFlow({
      container: containerRef.value,
      background: {
        backgroundColor: '#F8F9FF',
      },
      grid: true,
      // 一键设置所有不可编辑配置
      isSilentMode: true,

    });

    // 渲染图
    lf.value.render(initDrawData.value);
  }
}

async function onOpen(record: any) {
  setOpen(true);

  const res = await getDataArchitectureDataProjectDomainEntityGetRelation({
    Tableid: record.id,
  });

  // 使用拍平方法处理树形数据
  const flattenedData = res.data ? flattenTreeData(res.data) : [];

  // 将拍平后的数据转换为LogicFlow格式
  const logicFlowData = convertToLogicFlowData(flattenedData);
  console.warn('转换后的LogicFlow数据:', logicFlowData);

  // 如果有实际数据，使用转换后的数据；否则使用模拟数据
  if (flattenedData.length > 0) {
    initDrawData.value = logicFlowData;
  }
  // else {
  //   // 模拟数据（当API返回空数据时使用）
  //   initDrawData.value = {
  //     nodes: [
  //       {
  //         id: '1',
  //         type: 'rect',
  //         x: 100,
  //         y: 100,
  //         text: '节点1',
  //       },
  //       {
  //         id: '2',
  //         type: 'rect',
  //         x: 200,
  //         y: 200,
  //         text: '节点2',
  //       },
  //       {
  //         id: '3',
  //         type: 'rect',
  //         x: 300,
  //         y: 300,
  //         text: '节点3',
  //       },
  //     ],
  //     edges: [
  //       {
  //         id: '1-2',
  //         type: 'polyline',
  //         sourceNodeId: '1',
  //         targetNodeId: '2',
  //       },
  //       {
  //         id: '2-3',
  //         type: 'polyline',
  //         sourceNodeId: '2',
  //         targetNodeId: '3',
  //       },
  //     ],
  //   };
  // }

  // console.log(res, 'res'); // 调试用，已注释

  initFlowInstance();
}

function close() {
  setOpen(false);
}

defineExpose({
  open: onOpen,
});

onMounted(() => {

});
</script>

<template>
  <CustomDrawer
    v-model:open="open"
    title="关联关系"
    placement="right"
    :width="800"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }" @close="close"
  >
    <div ref="containerRef" class="h-full w-full" />
  </CustomDrawer>
</template>
