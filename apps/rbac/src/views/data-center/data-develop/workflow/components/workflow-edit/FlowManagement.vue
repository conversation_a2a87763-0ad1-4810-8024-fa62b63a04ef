<script setup lang="tsx">
import LogicFlow from '@logicflow/core';
import { <PERSON><PERSON>, MiniMap, Snapshot } from '@logicflow/extension';
import { message } from 'ant-design-vue';
import { provide } from 'vue';
import { useRouter } from 'vue-router';
import customBezier from './custom-edge/customBezier.ts';
import encryptNode from './custom-node/encrypt-node/encryptNode.ts';
import flinkNode from './custom-node/flink-node/flinkNode.ts';
import incrementalNode from './custom-node/incremental-node/incrementalNode.ts';
import pythonNode from './custom-node/python-node/pythonNode.ts';
import shellNode from './custom-node/shell-node/shellNode.ts';
import sqlNode from './custom-node/sql-node/sqlNode.ts';
import NodePanel from './nodePanel.vue';
import PropertyEditDrawer from './propertyEditDrawer.vue';
import ToolBar from './toolBar.vue';
import '@logicflow/core/lib/style/index.css';
import '@logicflow/extension/lib/style/index.css';

defineOptions({
  name: 'FlowManagement',
});

const router = useRouter();

const lf = ref<LogicFlow>();
const editRef = ref();
const isInitLf = ref(false);
const containerRef = ref<HTMLElement | null>(null);
const boxRef = ref<HTMLElement | null>(null);
const isFullscreen = ref(false);
const domainId = ref(router.currentRoute.value.query.domainId);
const workflowId = ref<string | null>(router.currentRoute.value.query.workflowId as string | null);

// 提供 workflowId 给子组件使用
provide('workflowId', workflowId);

const projectId = ref<number | null>(router.currentRoute.value.query.dsProjectCode as number | null);
const workflowDetail = ref<API.DataWorkflowDefineSave>({} as API.DataWorkflowDefineSave);
const initDrawData = ref<any>({});
const isOnlyView = ref(router.currentRoute.value.query.mode === 'view');

/*  添加下面这一段的原因是因为 apps/rbac/src/layouts/components/Tools/ScreenScale/index.vue 这个组件中有页面缩放的功能。
  如果页面被缩放后，而本页面没有相应的处理，会导致画布上元素连线时，线条定位不准。所以，我们需要在页面有更改时更新图的缩放比例。
 */
const zoomValue = ref(1); // 默认 zoom 值
function handleZoomChange() {
  const zoom = (document.documentElement.style as any).zoom || '1'; // 获取当前的 zoom 值
  zoomValue.value = Number.parseFloat(zoom); // 更新 zoom 值
  if (lf.value) {
    lf.value.zoom(zoom);
  }
}

let observer: MutationObserver | null = null;
function initZoomRatio() {
  observer = new MutationObserver(handleZoomChange);

  // 监听根节点的 style 属性变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['style'], // 只监听 style 属性的变化
  });

  // 初始时调用一次
  handleZoomChange();
}

// 注册节点和线
function registerNodes() {
  if (!lf.value) {
    return;
  };

  lf.value.register(shellNode);
  lf.value.register(sqlNode);
  lf.value.register(flinkNode);
  lf.value.register(pythonNode);
  lf.value.register(customBezier);
  lf.value.register(encryptNode);
  lf.value.register(incrementalNode);
  lf.value.setDefaultEdgeType('customBezier'); // 线类型，贝塞尔曲线
}

function registerEventListeners() {
  if (!lf.value) {
    return;
  }

  lf.value.on('connection:not-allowed', (data) => {
    message.error(data.msg);
  });

  lf.value.on('node:dbclick', ({ data }) => {
    editRef.value.open(data);
  });

  lf.value.on('node:dnd-add', ({ data }) => {
    editRef.value.open(data);
  });
}

function initFlowInstance() {
  if (containerRef.value) {
    lf.value = new LogicFlow({
      container: containerRef.value,
      plugins: [Menu, MiniMap, Snapshot],
      background: {
        backgroundColor: '#F8F9FF',
      },
      grid: true,
      nodeTextEdit: false, // 节点是否可编辑。false不可编辑
      edgeTextEdit: false, // 边是否可编辑。false不可编辑
    });
    isInitLf.value = true;

    // 注册节点和线
    registerNodes();

    // 渲染图
    lf.value.render(initDrawData.value);

    // 注册事件监听
    registerEventListeners();
  }
}

async function initProject() {
  if (projectId.value) {
    const res = await postDataWorkflowFindOne({
      id: workflowId.value!,
    });

    if (res.success) {
      workflowDetail.value = res.data as API.DataWorkflowDefineSave;

      const tempNodes = [];
      const tempEdges = [];

      const tempNodeLocationsMap: Record<string, { x: number, y: number }> = {};

      const { workflowInfo } = res.data as API.DataWorkflowDefineSave;
      const { locations, taskDefinitionJson, taskRelationJson } = workflowInfo as any;

      locations.forEach((location) => {
        tempNodeLocationsMap[location.taskCode] = {
          x: location.x,
          y: location.y,
        };
      });

      taskDefinitionJson.forEach((taskDefinition) => {
        const { code, taskType, frontEndType, ...rest } = taskDefinition;
        tempNodes.push({
          id: code,
          type: frontEndType || taskType,
          x: tempNodeLocationsMap[code].x,
          y: tempNodeLocationsMap[code].y,
          properties: {
            customProperties: {
              taskType,
              frontEndType,
              ...rest,
            },
          },
        });
      });

      taskRelationJson.forEach((taskRelation) => {
        if (taskRelation.preTaskCode !== '0') {
          tempEdges.push({
            type: 'customBezier',
            sourceNodeId: taskRelation.preTaskCode,
            targetNodeId: taskRelation.postTaskCode,
          });
        }
      });

      initDrawData.value = {
        nodes: tempNodes,
        edges: tempEdges,
      };
    }
  }
  else {
    const res = await postDataWorkflowFindOrCreateDsProject({
      id: Number(domainId.value),
      name: '',
    });

    if (res.success) {
      projectId.value = res.data ?? 0;
    }
  }
}

function onSubmit() {
  // console.log('onSubmit');
}

async function onSave() {

  // console.log('tempData', tempData);

  // console.log('tempTaskRelation', tempTaskRelation);
  // console.log('tempLocations', tempLocations);
  // console.log('tempTask', tempTask);
}

function onFullscreen(value: boolean) {
  const container = boxRef.value;
  if (!container) {
    return;
  }

  if (value) {
    if (container.requestFullscreen) {
      container.requestFullscreen();
    }
    else if ((container as any).webkitRequestFullscreen) {
      (container as any).webkitRequestFullscreen();
    }
    else if ((container as any).mozRequestFullScreen) {
      (container as any).mozRequestFullScreen();
    }
    else if ((container as any).msRequestFullscreen) {
      (container as any).msRequestFullscreen();
    }
    isFullscreen.value = true;
  }
  else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
    else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    }
    else if ((document as any).mozCancelFullScreen) {
      (document as any).mozCancelFullScreen();
    }
    else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }

    isFullscreen.value = false;
  }
}

onMounted(async () => {
  await initProject();
  initFlowInstance();
  initZoomRatio();
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});
</script>

<template>
  <div ref="boxRef" class="w-full h-full border border-solid border-[#E7E7E7] rounded-[4px]">
    <div class="toolbar h-[40px] flex items-center px-[16px] border-b border-[#e8e8e8] bg-[#FFF]">
      <ToolBar v-if="isInitLf" :lf="lf!" :project-id="projectId" :workflow-id="Number(workflowId)" :workflow-detail="workflowDetail" :is-only-view="isOnlyView" @fullscreen="onFullscreen" @save="onSave" />
    </div>

    <div class="h-[calc(100%-40px)] w-full relative">
      <!-- 左侧面板 -->
      <NodePanel v-if="isInitLf" :is-only-view="isOnlyView" :lf="lf!" class="absolute top-[10px] left-[10px] z-101" />
      <div ref="containerRef" class="h-full w-full" :style="{ zoom: 1 / zoomValue }" />
      <!-- 属性编辑器 -->
      <PropertyEditDrawer ref="editRef" :lf="lf!" :project-id="projectId!" @submit="onSubmit" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toolbar {
  :deep(.ant-btn-link) {
    height: 32px;
    padding: 4px 8px;
    color: #666;

    &:hover {
      color: #1890ff;
    }
  }
}
</style>
