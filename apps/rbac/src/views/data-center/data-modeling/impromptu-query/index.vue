<script setup lang="tsx">
import type { ProColumns } from '@pubinfo/pro-components';
import type { TreeSelectProps } from 'ant-design-vue';
import type { DataSourceItem as DataSourceItemType } from './types';
import { sql } from '@codemirror/lang-sql';
import { oneDark } from '@codemirror/theme-one-dark';
import { ProTable } from '@pubinfo/pro-components';
import { Codemirror } from 'vue-codemirror';
import { getUUID } from '@/utils/uuid';
import DataSourceItem from './components/dataSourceItem.vue';

defineOptions({
  name: 'ImpromptuQueryIndex',
});

const extensions = [sql(), oneDark];
const codemirrorRef = ref();
function handleBlur(e: any, item: any) {
  // console.log("handleBlur", e, item)
}

const columns = ref<ProColumns<any>>([]);

const form = reactive({
  sql: '',
});

const currentQueryKey = ref('');

const tableRef = ref();
const formRef = ref();

const rules = {
  sql: [{ required: true, message: '请输入SQL命令' }],
};

const dataSourceOption = ref<TreeSelectProps['treeData']>([]);

const dataSourceItemRefs = ref<any>({});

const dataSourceList = ref<DataSourceItemType[]>([
  {
    id: getUUID(),
    dataSource: undefined,
    dataTables: [],
  },
]);

let timer: any;

async function request(params: any) {
  const { current, pageSize, ...rest } = params;

  if (!currentQueryKey.value) {
    onQuery();
    return {
      success: true,
      data: [],
      total: 0,
    };
  }

  const res = await postDataWarehouseModeldataAdhocPageResult({
    currentPage: current,
    pageSize,
    key: currentQueryKey.value,
  });

  if (res.hint === 'false') {
    return new Promise((resolve) => {
      timer = setTimeout(async () => {
        const res = await request(params);
        resolve(res);
      }, 2000);

      return () => {
        clearTimeout(timer);
      };
    });
  }
  else {
    currentQueryKey.value = '';

    columns.value = res.data?.headers?.map((item: any) => ({
      title: item,
      dataIndex: item,
    })) || [];

    return {
      success: res.success,
      data: res.data?.records || [],
      total: res.data?.total || 0,
    };
  }
}

function onAddDataSource() {
  // console.log('onAddDataSource');
  dataSourceList.value.push({
    id: getUUID(),
    dataSource: undefined,
    dataTables: [],
  });
}

function onDeleteDataSource(id: string) {
  dataSourceList.value = dataSourceList.value.filter(item => item.id !== id);
}

async function createImpromptuQuery(isInit: boolean) {
  const tempKey = getUUID();
  const { current, pageSize } = tableRef.value.query;
  const res = await postDataWarehouseModeldataAdhocPageCreate({
    currentPage: current,
    pageSize,
    key: tempKey,
    sql: form.sql,
    dbSourceAndTablesList: dataSourceList.value.map(item => ({
      dbSourceId: item.dataSource,
      tableIds: item.dataTables,
    })),
  });

  currentQueryKey.value = tempKey;

  if (isInit) {
    tableRef.value.reload();
  }
  else {
    tableRef.value.fetch();
  }
}

function onQuery(isInit = false) {
  const dataSourcePromises = dataSourceList.value.map((item) => {
    return dataSourceItemRefs.value[item.id].submit();
  });

  Promise.all([
    formRef.value.validate(),
    ...dataSourcePromises,
  ]).then(() => {
    createImpromptuQuery(isInit);
  });
}

async function getDataSourceList() {
  const res = await getDataMetaTaskSource();

  const tempData = res?.data?.map((unit) => {
    return {
      id: getUUID(),
      name: unit.zhName,
      disabled: true,
      children: unit?.data?.map((item: any) => {
        return {
          id: item.db_source_id,
          name: item.name,
        };
      }) ?? [],
    };
  }) ?? [];

  dataSourceOption.value = tempData;
}
onMounted(() => {
  getDataSourceList();
});

onUnmounted(() => {
  clearTimeout(timer);
});
</script>

<template>
  <div
    w-full
    h-full
    py-29px
    px-40px
    flex
    flex-col
  >
    <div class="py-24px px-30px bg-[rgba(255,255,255,0.6)] rounded-t-[6px] flex items-center justify-between">
      <div class="text-[20px] text-[#161E5D] font-[600] leading-[23px]">
        即席查询
      </div>
      <a-button type="primary" @click="onAddDataSource">
        <template #icon>
          <PlusOutlined />
        </template>
        新增数据源
      </a-button>
    </div>
    <div class="flex-auto bg-[#FFF] p-[30px]">
      <div>
        <div class="border border-solid border-[#E5E5E5] rounded-[6px] mb-30px">
          <div v-for="(item, index) in dataSourceList" :key="item.id" class="flex items-center gap-x-24px p-r-24px" :class="{ 'border-b-1 border-b-solid border-[#E5E5E5]': index !== dataSourceList.length - 1 }">
            <DataSourceItem
              :ref="(el: any) => { dataSourceItemRefs[item.id] = el }"
              :init-info="item"
              class="flex-1"
              :data-source-option="dataSourceOption"
            />
            <div v-if="dataSourceList.length > 1" class="cursor-pointer" @click="onDeleteDataSource(item.id)">
              <DeleteOutlined />
            </div>
          </div>
        </div>
        <div class="mb-30px">
          <div class="form-group-title">
            <img src="@/assets/images/data-gather/title-sql.png">
            <span>SQL命令</span>
          </div>
          <a-form
            ref="formRef"
            layout="vertical"
            :model="form"
            :rules="rules"
          >
            <a-form-item label="SQL命令" name="sql">
              <Codemirror
                ref="codemirrorRef" v-model="form.sql" placeholder="请输入..."
                :style="{ width: '100%', height: '240px' }" :autofocus="true" :extensions="extensions"
                :indent-with-tab="true" :tab-size="2" @blur="handleBlur($event, form)"
              />
            </a-form-item>
          </a-form>
          <div class="mt-24px">
            <a-button type="primary" @click="onQuery(true)">
              查询
            </a-button>
          </div>
        </div>
        <div>
          <div class="form-group-title">
            <img src="@/assets/images/data-gather/title-cksjyl.png">
            <span>查看结果</span>
          </div>
          <ProTable
            ref="tableRef"
            row-key="id"
            :request="request"
            :columns="columns"
            :search="false"
            :toolbar="false"
            :manual-request="true"
            :scroll="{ x: 800 }"
          >
            <template #bodyCell="{ index, column }">
              <template v-if="column.dataIndex === 'index'">
                {{ index + 1 }}
              </template>
            </template>
          </ProTable>
        </div>
      </div>
    </div>
  </div>
</template>
