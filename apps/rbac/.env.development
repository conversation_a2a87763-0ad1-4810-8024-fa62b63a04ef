###
 # @Description:
 # @Author: wsy
 # @Date: 2023-11-20 09:08:32
 # @LastEditTime: 2024-07-31 15:34:21
 # @LastEditors: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
###
# 接口请求地址，会设置到 axios 的 baseURL 参数上
# VITE_APP_API_BASEURL = http://***************:20001/pubinfo-sys
# VITE_APP_API_BASEURL = http://************:9099/data-center-backend
# VITE_APP_API_BASEURL = http://************:7676/gjjxy-backend

# VITE_APP_API_BASEURL = http://************:9099/data-center-backend
# 本地
VITE_APP_API_BASEURL = http://*************:9099/data-center-backend
# VITE_APP_API_BASEURL = http://**************:6099/data-center-backend


#第三方获取token的api
VITE_AUTH_API_URL = http://*************:6099

# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否开启代理
VITE_OPEN_PROXY = false
# 是否启动 vite app inspector
VITE_APP_INSPECTOR = false

# 是否开启全局加密
VITE_GLOBAL_ENCRYPT_CONFIG = true
